const express = require('express');
const router = express.Router();
const { requireLogin } = require('../utils/auth');
const User = require('../models/user');
const Link = require('../models/link');
const Device = require('../models/device');
const Task = require('../models/task');
const OperationLog = require('../models/operation_log');
const Setting = require('../models/setting');
const moment = require('moment');
const db = require('../utils/db');
const DeviceConfig = require('../models/device_config');

// 首页/登录页
router.get('/', (req, res) => {
  if (req.session && req.session.userId) {
    console.log('用户已登录，重定向到仪表盘，用户ID:', req.session.userId);
    return res.redirect('/dashboard');
  }
  console.log('用户未登录，显示登录页面');
  res.render('login', { error: null });
});

// 处理登录
router.post('/login', async (req, res) => {
  try {
    const { username, token } = req.body;
    console.log('收到登录请求，用户名:', username, '密码:', token);
    
    if (!username || !token) {
      console.log('用户名或密码为空');
      return res.render('login', { error: '请输入用户名和密码' });
    }
    
    // 从数据库验证用户
    const user = await User.getByUsernameAndToken(username, token);
    if (!user) {
      console.log('用户名或密码无效');
      return res.render('login', { error: '用户名或密码无效' });
    }
    
    console.log('找到用户:', user.id);
    // 设置会话
    req.session.userId = user.id;
    console.log('已设置会话，用户ID:', req.session.userId);
    
    // 检查用户设置是否已初始化
    const settings = await Setting.getAll(user.id);
    console.log('获取到的设置:', settings);
    if (!settings || settings.length === 0) {
      console.log('初始化用户设置');
      // 初始化用户设置
      await Setting.initUserSettings(user.id);
    }
    
    console.log('登录成功，重定向到仪表盘');
    // 使用302强制重定向，确保浏览器更新URL
    res.writeHead(302, {
      'Location': '/dashboard'
    });
    return res.end();
  } catch (error) {
    console.error('登录错误:', error);
    return res.render('login', { error: '服务器错误' });
  }
});

// 登出
router.get('/logout', (req, res) => {
  req.session.destroy();
  res.redirect('/');
});

// 强制重置会话并重定向到仪表盘（用于修复导航问题）
router.get('/reset-navigation', requireLogin, (req, res) => {
  // 保存用户ID
  const userId = req.user.id;
  
  // 重置会话
  req.session.regenerate((err) => {
    if (err) {
      console.error('重置会话错误:', err);
      return res.render('error', { message: '重置会话失败' });
    }
    
    // 重新设置用户ID
    req.session.userId = userId;
    
    // 强制重定向到仪表盘
    res.writeHead(302, {
      'Location': '/dashboard',
      'Cache-Control': 'no-store, no-cache, must-revalidate, post-check=0, pre-check=0',
      'Pragma': 'no-cache',
      'Expires': '0'
    });
    res.end();
  });
});

// 导航修复页面
router.get('/fix-navigation', requireLogin, (req, res) => {
  res.render('fix-navigation');
});

// 仪表盘
router.get('/dashboard', requireLogin, async (req, res) => {
  try {
    console.log('访问仪表盘，用户ID:', req.user.id);
    
    // 获取用户的数据统计
    console.log('获取用户数据统计...');
    const linkCount = await User.getLinkCount(req.user.id);
    const deviceCount = await User.getDeviceCount(req.user.id);
    const todayStats = await OperationLog.getTodayStats(req.user.id);
    const recentLogs = await OperationLog.getByUserId(req.user.id, 10);
    
    // 确保字段名称一致性
    for (const log of recentLogs) {
      // 如果新字段不存在但旧字段存在，则使用旧字段值
      if (log.like_count === undefined && log.likes_count !== undefined) {
        log.like_count = log.likes_count;
      }
      if (log.collect_count === undefined && log.collects_count !== undefined) {
        log.collect_count = log.collects_count;
      }
    }
    
    // 获取任务相关统计
    console.log('获取任务相关统计...');
    const tasks = await Task.getAllByUserId(req.user.id);
    const taskCount = tasks.length;
    
    // 获取活跃任务数量
    const activeTaskCount = tasks.filter(task => task.status === 'active').length;
    
    console.log('渲染仪表盘页面...');
    res.render('dashboard', {
      user: req.user,
      linkCount,
      deviceCount,
      todayStats,
      recentLogs,
      taskCount,
      activeTaskCount,
      moment
    });
  } catch (error) {
    console.error('仪表盘错误:', error);
    res.render('error', { message: '加载仪表盘数据失败' });
  }
});

// 链接列表页
router.get('/links', requireLogin, async (req, res) => {
  try {
    const filters = {
      status: req.query.status,
      priority: req.query.priority,
      batch: req.query.batch,
      task_id: req.query.task_id ? parseInt(req.query.task_id) : null,
      failCount: req.query.failCount ? parseInt(req.query.failCount) : null,
      dateFrom: req.query.dateFrom,
      dateTo: req.query.dateTo,
      orderBy: req.query.orderBy || 'created_at',
      orderDir: req.query.orderDir || 'DESC',
      search: req.query.search || ''
    };
    
    // 分页参数
    const page = parseInt(req.query.page) || 1;
    const perPage = parseInt(req.query.perPage) || parseInt(res.locals.globalSettings?.['general.items_per_page']) || 20;
    const offset = (page - 1) * perPage;
    
    // 获取总链接数
    const totalLinks = await Link.countByUserId(req.user.id, filters);
    const totalPages = Math.ceil(totalLinks / perPage);
    
    // 获取所有批次
    const batches = await Link.getAllBatches(req.user.id);
    
    // 获取所有任务
    const tasks = await Task.getAllByUserId(req.user.id);
    
    // 获取链接列表（带分页）
    const links = await Link.getAllByUserId(req.user.id, filters, perPage, offset);
    
    // 为每个链接添加任务名称
    for (const link of links) {
      if (link.task_id) {
        const task = tasks.find(t => t.id === link.task_id);
        if (task) {
          link.task_name = task.name;
        }
      }
    }
    
    // 生成分页URL的辅助函数
    const getPageUrl = (pageNum) => {
      const url = new URL(`${req.protocol}://${req.get('host')}${req.originalUrl}`);
      url.searchParams.set('page', pageNum);
      return url.pathname + url.search;
    };
    
    res.render('links/index', {
      user: req.user,
      links,
      filters,
      batches,
      tasks,
      moment,
      currentPage: page,
      perPage,
      totalPages,
      getPageUrl,
      originalUrl: req.originalUrl
    });
  } catch (error) {
    console.error('链接列表错误:', error);
    res.render('error', { message: '加载链接数据失败' });
  }
});

// 批量导入链接页面
router.get('/links/batch-import', requireLogin, async (req, res) => {
  res.status(404).render('error', { message: '页面不存在' });
});

// 处理批量导入链接
router.post('/links/batch-import', requireLogin, async (req, res) => {
  res.status(404).render('error', { message: '页面不存在' });
});

// 添加链接页面
router.get('/links/create', requireLogin, async (req, res) => {
  res.status(404).render('error', { message: '页面不存在' });
});

// 处理添加链接
router.post('/links/create', requireLogin, async (req, res) => {
  res.status(404).render('error', { message: '页面不存在' });
});

// 编辑链接页面
router.get('/links/edit/:id', requireLogin, async (req, res) => {
  try {
    const link = await Link.getById(req.params.id);
    
    if (!link) {
      return res.redirect('/links');
    }
    
    if (link.user_id !== req.user.id) {
      return res.redirect('/links');
    }
    
    // 获取用户的所有任务，用于选择关联任务
    const tasks = await Task.getAllByUserId(req.user.id);
    
    // 获取来源页面URL
    const referer = req.query.referer || req.headers.referer || '/links';
    console.log('编辑链接GET - 来源页面:', referer);
    
    res.render('links/edit', { 
      user: req.user, 
      link, 
      error: null,
      tasks,
      referer
    });
  } catch (error) {
    console.error('编辑链接错误:', error);
    res.redirect('/links');
  }
});

// 处理编辑链接
router.post('/links/edit/:id', requireLogin, async (req, res) => {
  try {
    const linkId = req.params.id;
    const { 
      url, original_likes, original_collects, original_comments,
      current_likes, current_collects, current_comments,
      target_likes, target_collects, target_comments,
      priority, status, task_id, referer
    } = req.body;
    
    console.log('编辑链接POST - 来源页面:', referer);
    
    // 获取当前链接
    const currentLink = await Link.getById(linkId);
    if (!currentLink || currentLink.user_id !== req.user.id) {
      return res.redirect(referer || '/links');
    }
    
    if (!task_id) {
      const tasks = await Task.getAllByUserId(req.user.id);
      return res.render('links/edit', { 
        user: req.user, 
        link: currentLink,
        error: '请选择一个任务',
        tasks,
        referer
      });
    }
    
    // 验证任务是否存在且属于当前用户
    const task = await Task.getById(task_id);
    if (!task || task.user_id !== req.user.id) {
      const tasks = await Task.getAllByUserId(req.user.id);
      return res.render('links/edit', { 
        user: req.user, 
        link: currentLink,
        error: '所选任务无效',
        tasks,
        referer
      });
    }
    
    // 如果URL改变了，检查新URL是否在同一任务中已存在
    if (url !== currentLink.url) {
      const existingLink = await Link.checkExistsInTask(req.user.id, url, task_id);
      if (existingLink && existingLink.id !== parseInt(linkId)) {
        const tasks = await Task.getAllByUserId(req.user.id);
        return res.render('links/edit', { 
          user: req.user, 
          link: currentLink,
          error: '该链接已在所选任务中存在',
          tasks,
          referer
        });
      }
    }
    
    // 如果任务改变了，检查URL是否在新任务中已存在
    if (parseInt(task_id) !== currentLink.task_id && url === currentLink.url) {
      const existingLink = await Link.checkExistsInTask(req.user.id, url, task_id);
      if (existingLink && existingLink.id !== parseInt(linkId)) {
        const tasks = await Task.getAllByUserId(req.user.id);
        return res.render('links/edit', { 
          user: req.user, 
          link: currentLink,
          error: '该链接已在所选任务中存在',
          tasks,
          referer
        });
      }
    }
    
    const linkData = {
      url,
      task_id,
      original_likes: parseInt(original_likes) || 0,
      original_collects: parseInt(original_collects) || 0,
      original_comments: parseInt(original_comments) || 0,
      current_likes: parseInt(current_likes) || 0,
      current_collects: parseInt(current_collects) || 0,
      current_comments: parseInt(current_comments) || 0,
      target_likes: parseInt(target_likes) || 0,
      target_collects: parseInt(target_collects) || 0,
      target_comments: parseInt(target_comments) || 0,
      priority: priority || 'medium',
      status: status || 'active'
    };
    
    await Link.update(linkId, linkData);
    
    console.log('编辑链接完成 - 重定向到:', referer || '/links');
    res.redirect(referer || '/links');
  } catch (error) {
    console.error('编辑链接错误:', error);
    res.redirect(req.body.referer || '/links');
  }
});

// 删除链接
router.post('/links/delete/:id', requireLogin, async (req, res) => {
  console.log('接收到删除请求, ID:', req.params.id);
  console.log('请求头:', {
    xhr: req.xhr,
    accept: req.headers.accept,
    'x-requested-with': req.headers['x-requested-with'],
    'content-type': req.headers['content-type']
  });
  
  // 获取重定向URL，优先使用表单中的redirect_to参数
  const redirectTo = req.body.redirect_to || req.headers.referer || '/links';
  console.log('重定向到:', redirectTo);

  try {
    const link = await Link.getById(req.params.id);
    console.log('找到链接:', link ? link.id : '未找到');
    
    if (!link || link.user_id !== req.user.id) {
      console.log('无权删除链接, 用户ID:', req.user.id, '链接所有者:', link ? link.user_id : '未知');
      
      // 检查是否为AJAX请求
      if (req.xhr || req.headers['x-requested-with'] === 'XMLHttpRequest') {
        console.log('以AJAX方式返回授权错误');
        return res.status(403).json({ success: false, message: '无权删除此链接' });
      }
      
      console.log('重定向到:', redirectTo);
      return res.redirect(redirectTo);
    }
    
    await Link.delete(link.id);
    console.log('链接已删除, ID:', link.id);
    
    // 检查是否为AJAX请求
    if (req.xhr || req.headers['x-requested-with'] === 'XMLHttpRequest') {
      console.log('以AJAX方式返回成功');
      return res.json({ success: true });
    }
    
    // 非AJAX请求，重定向到指定页面
    console.log('重定向到:', redirectTo);
    res.redirect(redirectTo);
  } catch (error) {
    console.error('删除链接错误:', error);
    
    // 检查是否为AJAX请求
    if (req.xhr || req.headers['x-requested-with'] === 'XMLHttpRequest') {
      console.log('以AJAX方式返回错误');
      return res.status(500).json({ success: false, message: '删除链接失败: ' + error.message });
    }
    
    console.log('重定向到:', redirectTo);
    res.redirect(redirectTo);
  }
});

// 直接删除链接（GET方式）
router.get('/links/delete-direct/:id', requireLogin, async (req, res) => {
  console.log('接收到直接删除请求, ID:', req.params.id);
  
  // 获取重定向URL
  const redirectTo = req.query.redirect_to || '/links';
  console.log('重定向到:', redirectTo);
  
  try {
    const link = await Link.getById(req.params.id);
    console.log('找到链接:', link ? link.id : '未找到');
    
    if (!link || link.user_id !== req.user.id) {
      console.log('无权删除链接, 用户ID:', req.user.id, '链接所有者:', link ? link.user_id : '未知');
      return res.redirect(redirectTo);
    }
    
    await Link.delete(link.id);
    console.log('链接已删除, ID:', link.id);
    
    // 重定向到指定页面
    res.redirect(redirectTo);
  } catch (error) {
    console.error('删除链接错误:', error);
    res.redirect(redirectTo);
  }
});

// 更改链接状态（暂停/激活）
router.post('/links/status/:id', requireLogin, async (req, res) => {
  // 获取返回URL或使用HTTP Referer作为备用
  const returnUrl = req.body.returnUrl ? decodeURIComponent(req.body.returnUrl) : (req.headers.referer || '/links');
  
  try {
    console.log('更改链接状态请求:', req.params.id, req.body.status, '返回到:', returnUrl);
    const link = await Link.getById(req.params.id);
    
    if (!link || link.user_id !== req.user.id) {
      // 检查是否为AJAX请求
      if (req.xhr || req.headers['x-requested-with'] === 'XMLHttpRequest') {
        return res.status(403).json({ success: false, message: '无权修改此链接' });
      }
      
      return res.redirect(returnUrl);
    }
    
    const { status } = req.body;
    
    // 验证状态值
    if (status !== 'active' && status !== 'paused' && status !== 'completed' && status !== 'error') {
      if (req.xhr || req.headers['x-requested-with'] === 'XMLHttpRequest') {
        return res.status(400).json({ success: false, message: '无效的状态值' });
      }
      
      return res.redirect(returnUrl);
    }
    
    // 更新链接状态
    await Link.updateStatus(link.id, status);
    console.log('链接状态已更新:', link.id, status);
    
    // 检查是否为AJAX请求
    if (req.xhr || req.headers['x-requested-with'] === 'XMLHttpRequest') {
      return res.json({ 
        success: true,
        link: {
          id: link.id,
          status: status
        }
      });
    }
    
    // 非AJAX请求，重定向回返回URL
    console.log('重定向到:', returnUrl);
    res.redirect(returnUrl);
  } catch (error) {
    console.error('更改链接状态错误:', error);
    
    // 检查是否为AJAX请求
    if (req.xhr || req.headers['x-requested-with'] === 'XMLHttpRequest') {
      return res.status(500).json({ success: false, message: '更改链接状态失败: ' + error.message });
    }
    
    res.redirect(returnUrl);
  }
});

// 设备列表页
router.get('/devices', requireLogin, async (req, res) => {
  try {
    const devices = await Device.getAllByUserId(req.user.id);
    
    // 获取每个设备的统计数据
    for (const device of devices) {
      device.todayCount = await Device.getTodayOperationCount(device.id);
      device.totalCount = await Device.getTotalOperationCount(device.id);
      device.successRate = await Device.getSuccessRate(device.id);
    }
    
    res.render('devices/index', {
      user: req.user,
      devices,
      moment
    });
  } catch (error) {
    console.error('设备列表错误:', error);
    res.render('error', { message: '加载设备数据失败' });
  }
});

// 添加设备页面
router.get('/devices/create', requireLogin, (req, res) => {
  res.render('devices/create', { 
    user: req.user, 
    error: null 
  });
});

// 处理添加设备
router.post('/devices/create', requireLogin, async (req, res) => {
  try {
    const { device_name } = req.body;
    
    if (!device_name) {
      return res.render('devices/create', { 
        user: req.user, 
        error: '请输入设备名称' 
      });
    }
    
    // 生成唯一设备令牌
    const device_token = `${req.user.id}_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;
    
    const deviceData = {
      user_id: req.user.id,
      device_name,
      device_token
    };
    
    await Device.create(deviceData);
    
    res.redirect('/devices');
  } catch (error) {
    console.error('添加设备错误:', error);
    res.render('devices/create', { 
      user: req.user, 
      error: '添加设备失败' 
    });
  }
});

// 编辑设备页面
router.get('/devices/edit/:id', requireLogin, async (req, res) => {
  try {
    const device = await Device.getById(req.params.id);
    
    if (!device) {
      return res.redirect('/devices');
    }
    
    if (device.user_id !== req.user.id) {
      return res.redirect('/devices');
    }
    
    res.render('devices/edit', { 
      user: req.user, 
      device, 
      error: null 
    });
  } catch (error) {
    console.error('编辑设备错误:', error);
    res.redirect('/devices');
  }
});

// 处理编辑设备
router.post('/devices/edit/:id', requireLogin, async (req, res) => {
  try {
    const device = await Device.getById(req.params.id);
    
    if (!device || device.user_id !== req.user.id) {
      return res.redirect('/devices');
    }
    
    const { device_name, status } = req.body;
    
    const deviceData = {
      device_name,
      device_token: device.device_token,
      status: status || 'active'
    };
    
    await Device.update(device.id, deviceData);
    
    res.redirect('/devices');
  } catch (error) {
    console.error('更新设备错误:', error);
    const device = await Device.getById(req.params.id);
    res.render('devices/edit', { 
      user: req.user, 
      device, 
      error: '更新设备失败' 
    });
  }
});

// 删除设备 (支持GET和POST)
router.get('/devices/delete/:id', requireLogin, async (req, res) => {
  try {
    const device = await Device.getById(req.params.id);

    if (!device || device.user_id !== req.user.id) {
      return res.redirect('/devices');
    }

    await Device.delete(device.id);

    res.redirect('/devices');
  } catch (error) {
    console.error('删除设备错误:', error);
    res.redirect('/devices');
  }
});

router.post('/devices/delete/:id', requireLogin, async (req, res) => {
  try {
    const device = await Device.getById(req.params.id);

    if (!device || device.user_id !== req.user.id) {
      return res.redirect('/devices');
    }

    await Device.delete(device.id);

    res.redirect('/devices');
  } catch (error) {
    console.error('删除设备错误:', error);
    res.redirect('/devices');
  }
});

// 批量删除设备
router.post('/devices/batch-delete', requireLogin, async (req, res) => {
  try {
    const { type, customDate } = req.body;
    const userId = req.user.id;
    let deletedCount = 0;

    switch(type) {
      case '3_days_ago':
        deletedCount = await Device.batchDeleteByDaysAgo(userId, 3);
        break;
      case '7_days_ago':
        deletedCount = await Device.batchDeleteByDaysAgo(userId, 7);
        break;
      case '15_days_ago':
        deletedCount = await Device.batchDeleteByDaysAgo(userId, 15);
        break;
      case '30_days_ago':
        deletedCount = await Device.batchDeleteByDaysAgo(userId, 30);
        break;
      case 'custom_date':
        if (!customDate) {
          return res.json({ success: false, message: '请提供自定义日期' });
        }
        deletedCount = await Device.batchDeleteBeforeDate(userId, customDate);
        break;
      case 'never_active':
        deletedCount = await Device.batchDeleteNeverActive(userId);
        break;
      case 'all':
        deletedCount = await Device.batchDeleteAll(userId);
        break;
      default:
        return res.json({ success: false, message: '无效的删除类型' });
    }

    res.json({
      success: true,
      deletedCount: deletedCount,
      message: `成功删除 ${deletedCount} 个设备`
    });
  } catch (error) {
    console.error('批量删除设备错误:', error);
    res.json({
      success: false,
      message: '批量删除失败: ' + error.message
    });
  }
});

// 数据统计页面
router.get('/stats', requireLogin, async (req, res) => {
  try {
    let days = parseInt(req.query.days) || 7;
    let startDate = null;
    let endDate = null;

    // 处理特殊类型的日期选择
    if (req.query.type === 'today') {
      startDate = moment().format('YYYY-MM-DD');
      endDate = moment().format('YYYY-MM-DD');
      days = 1;
    } else if (req.query.type === 'yesterday') {
      startDate = moment().subtract(1, 'day').format('YYYY-MM-DD');
      endDate = moment().subtract(1, 'day').format('YYYY-MM-DD');
      days = 1;
    } else if (req.query.start_date && req.query.end_date) {
      startDate = req.query.start_date;
      endDate = req.query.end_date;
      // 计算天数差
      const start = moment(startDate);
      const end = moment(endDate);
      days = end.diff(start, 'days') + 1;
    }

    // 获取每日统计数据
    const dailyStats = await OperationLog.getDailyStats(req.user.id, days, startDate, endDate);

    // 链接统计
    const links = await Link.getAllByUserId(req.user.id);
    const linkStats = {
      total: links.length,
      active: links.filter(l => l.status === 'active').length,
      completed: links.filter(l => l.status === 'completed').length,
      error: links.filter(l => l.status === 'error').length,
      paused: links.filter(l => l.status === 'paused').length
    };

    // 设备统计
    const devices = await Device.getAllByUserId(req.user.id);
    const deviceStats = {
      total: devices.length,
      active: devices.filter(d => d.status === 'active').length,
      inactive: devices.filter(d => d.status === 'inactive').length
    };

    // 任务统计
    const tasks = await Task.getAllByUserId(req.user.id);
    const taskStats = {
      total: tasks.length,
      active: tasks.filter(t => t.status === 'active').length,
      completed: tasks.filter(t => t.status === 'completed').length,
      paused: tasks.filter(t => t.status === 'paused').length
    };

    // 操作统计（总计）
    const totalOperations = await OperationLog.getTotalStats(req.user.id) || {};

    // 最近活跃设备
    const recentDevices = await Device.getRecentActiveDevices(req.user.id, 5) || [];

    // 热门链接（操作次数最多的前5个）
    const topLinks = await Link.getTopLinksByOperations(req.user.id, 5) || [];

    // 效率统计
    const efficiencyStats = await OperationLog.getEfficiencyStats(req.user.id, days) || {};

    res.render('stats', {
      user: req.user,
      req: req,
      dailyStats,
      linkStats,
      deviceStats,
      taskStats,
      totalOperations,
      recentDevices,
      topLinks,
      efficiencyStats,
      days,
      moment
    });
  } catch (error) {
    console.error('数据统计错误:', error);
    res.render('error', { message: '加载统计数据失败' });
  }
});

// 批量更新链接状态
router.post('/links/batch-update-status', requireLogin, async (req, res) => {
  try {
    const { linkIds, status } = req.body;

    if (!linkIds || !Array.isArray(linkIds) || linkIds.length === 0) {
      return res.status(400).json({ success: false, message: '请选择要操作的链接' });
    }

    if (!['active', 'paused', 'completed', 'error'].includes(status)) {
      return res.status(400).json({ success: false, message: '无效的状态值' });
    }

    // 验证所有链接都属于当前用户
    const links = await Link.getByIds(linkIds);
    const userLinks = links.filter(link => link.user_id === req.user.id);

    if (userLinks.length !== linkIds.length) {
      return res.status(403).json({ success: false, message: '部分链接不属于当前用户' });
    }

    // 批量更新状态
    const result = await Link.batchUpdateStatus(linkIds, status);

    res.json({
      success: true,
      message: `成功更新 ${result.affectedRows} 个链接的状态`,
      affectedRows: result.affectedRows
    });
  } catch (error) {
    console.error('批量更新链接状态错误:', error);
    res.status(500).json({
      success: false,
      message: '批量更新失败: ' + error.message
    });
  }
});

// 批量删除链接
router.post('/links/batch-delete', requireLogin, async (req, res) => {
  try {
    const { linkIds } = req.body;

    if (!linkIds || !Array.isArray(linkIds) || linkIds.length === 0) {
      return res.status(400).json({ success: false, message: '请选择要删除的链接' });
    }

    // 验证所有链接都属于当前用户
    const links = await Link.getByIds(linkIds);
    const userLinks = links.filter(link => link.user_id === req.user.id);

    if (userLinks.length !== linkIds.length) {
      return res.status(403).json({ success: false, message: '部分链接不属于当前用户' });
    }

    // 批量删除
    const result = await Link.batchDelete(linkIds);

    res.json({
      success: true,
      message: `成功删除 ${result.affectedRows} 个链接`,
      affectedRows: result.affectedRows
    });
  } catch (error) {
    console.error('批量删除链接错误:', error);
    res.status(500).json({
      success: false,
      message: '批量删除失败: ' + error.message
    });
  }
});

// 批次操作
router.post('/links/batch-action', requireLogin, async (req, res) => {
  try {
    const { batch, action } = req.body;
    
    if (!batch) {
      return res.redirect('/links');
    }
    
    let result;
    let message;
    
    switch (action) {
      case 'activate':
        result = await Link.activateBatch(req.user.id, batch);
        message = `已激活批次 "${batch}" 中的 ${result.affectedRows} 个链接`;
        break;
      case 'pause':
        result = await Link.pauseBatch(req.user.id, batch);
        message = `已暂停批次 "${batch}" 中的 ${result.affectedRows} 个链接`;
        break;
      case 'reset':
        result = await Link.resetBatchFailCount(req.user.id, batch);
        message = `已重置批次 "${batch}" 中 ${result.affectedRows} 个链接的失败次数`;
        break;
      default:
        message = '未知操作';
    }
    
    // 添加一个闪存消息（需要在app.js中配置connect-flash）
    // req.flash('success', message);
    
    return res.redirect(`/links?batch=${encodeURIComponent(batch)}`);
  } catch (error) {
    console.error('批次操作错误:', error);
    return res.redirect('/links');
  }
});

// 任务列表页
router.get('/tasks', requireLogin, async (req, res) => {
  try {
    const filters = {
      status: req.query.status,
      priority: req.query.priority,
      orderBy: req.query.orderBy || 'created_at',
      orderDir: req.query.orderDir || 'DESC'
    };
    
    // 分页参数
    const page = parseInt(req.query.page) || 1;
    const perPage = parseInt(req.query.perPage) || parseInt(res.locals.globalSettings?.['general.items_per_page']) || 20;
    const offset = (page - 1) * perPage;
    
    // 获取总任务数
    const totalTasks = await Task.countByUserId(req.user.id, filters);
    const totalPages = Math.ceil(totalTasks / perPage);
    
    // 获取分页后的任务列表
    const tasks = await Task.getAllByUserId(req.user.id, filters, perPage, offset);
    
    // 获取每个任务的统计数据
    for (const task of tasks) {
      task.linkCount = await Task.getLinkCount(task.id);
      task.activeCount = await Task.getActiveLinkCount(task.id);
      task.completedCount = await Task.getCompletedLinkCount(task.id);
      task.errorCount = await Task.getErrorLinkCount(task.id);
      task.progress = await Task.getProgress(task.id);
      // 添加链接数量到任务对象
      task.link_count = task.linkCount;
      // 计算完成百分比
      task.completion_percentage = task.linkCount > 0 ? (task.completedCount / task.linkCount) * 100 : 0;
    }
    
    // 生成分页URL的辅助函数
    const getPageUrl = (pageNum) => {
      const url = new URL(`${req.protocol}://${req.get('host')}${req.originalUrl}`);
      url.searchParams.set('page', pageNum);
      return url.pathname + url.search;
    };
    
    res.render('tasks/index', {
      user: req.user,
      tasks,
      filters,
      moment,
      currentPage: page,
      perPage,
      totalPages,
      getPageUrl,
      originalUrl: req.originalUrl
    });
  } catch (error) {
    console.error('任务列表错误:', error);
    res.render('error', { message: '加载任务数据失败' });
  }
});

// 更改任务状态（从任务列表页面）
router.post('/tasks/pause/:id', requireLogin, async (req, res) => {
  try {
    console.log('收到任务暂停请求:', req.params.id);
    const task = await Task.getById(req.params.id);
    
    if (!task || task.user_id !== req.user.id) {
      console.log('任务不存在或不属于当前用户');
      return res.redirect('/tasks');
    }
    
    // 更新任务状态
    await Task.update(task.id, { status: 'paused' });
    console.log('已暂停任务:', task.id);
    
    // 同时暂停任务中所有链接的状态
    await Link.pauseTaskLinks(task.id);
    console.log('已暂停任务中的链接');
    
    // 重定向到返回URL或任务列表页面
    const returnUrl = req.body.returnUrl ? decodeURIComponent(req.body.returnUrl) : '/tasks';
    console.log('重定向到:', returnUrl);
    res.redirect(returnUrl);
  } catch (error) {
    console.error('暂停任务错误:', error);
    res.redirect('/tasks');
  }
});

router.post('/tasks/activate/:id', requireLogin, async (req, res) => {
  try {
    console.log('收到任务激活请求:', req.params.id);
    const task = await Task.getById(req.params.id);
    
    if (!task || task.user_id !== req.user.id) {
      console.log('任务不存在或不属于当前用户');
      return res.redirect('/tasks');
    }
    
    // 更新任务状态
    await Task.update(task.id, { status: 'active' });
    console.log('已激活任务:', task.id);
    
    // 同时激活任务中所有链接的状态
    await Link.activateTaskLinks(task.id);
    console.log('已激活任务中的链接');
    
    // 重定向到返回URL或任务列表页面
    const returnUrl = req.body.returnUrl ? decodeURIComponent(req.body.returnUrl) : '/tasks';
    console.log('重定向到:', returnUrl);
    res.redirect(returnUrl);
  } catch (error) {
    console.error('激活任务错误:', error);
    res.redirect('/tasks');
  }
});

// 添加任务页面
router.get('/tasks/create', requireLogin, (req, res) => {
  res.render('tasks/create', { 
    user: req.user, 
    error: null 
  });
});

// 处理添加任务
router.post('/tasks/create', requireLogin, async (req, res) => {
  try {
    const { 
      name, description, priority, target_likes, target_collects, target_comments 
    } = req.body;
    
    if (!name) {
      return res.render('tasks/create', { 
        user: req.user, 
        error: '请输入任务名称' 
      });
    }
    
    // 获取默认设置
    const defaultPriority = res.locals.globalSettings ? res.locals.globalSettings['task.default_priority'] : 'medium';
    const defaultTargetLikes = res.locals.globalSettings ? parseInt(res.locals.globalSettings['task.default_target_likes']) : 100;
    const defaultTargetCollects = res.locals.globalSettings ? parseInt(res.locals.globalSettings['task.default_target_collects']) : 0;
    const defaultTargetComments = res.locals.globalSettings ? parseInt(res.locals.globalSettings['task.default_target_comments']) : 0;
    
    const taskData = {
      user_id: req.user.id,
      name,
      description,
      status: 'active',
      priority: priority || defaultPriority,
      target_likes: parseInt(target_likes) || defaultTargetLikes,
      target_collects: parseInt(target_collects) || defaultTargetCollects,
      target_comments: parseInt(target_comments) || defaultTargetComments
    };
    
    await Task.create(taskData);
    
    res.redirect('/tasks');
  } catch (error) {
    console.error('添加任务错误:', error);
    res.render('tasks/create', { 
      user: req.user, 
      error: '添加任务失败' 
    });
  }
});

// 编辑任务页面
router.get('/tasks/edit/:id', requireLogin, async (req, res) => {
  try {
    const task = await Task.getById(req.params.id);
    
    if (!task) {
      return res.redirect('/tasks');
    }
    
    if (task.user_id !== req.user.id) {
      return res.redirect('/tasks');
    }
    
    res.render('tasks/edit', { 
      user: req.user, 
      task, 
      error: null 
    });
  } catch (error) {
    console.error('编辑任务错误:', error);
    res.redirect('/tasks');
  }
});

// 处理编辑任务
router.post('/tasks/edit/:id', requireLogin, async (req, res) => {
  try {
    const task = await Task.getById(req.params.id);
    
    if (!task || task.user_id !== req.user.id) {
      return res.redirect('/tasks');
    }
    
    const { 
      name, description, status, priority, target_likes, target_collects, target_comments
    } = req.body;
    
    const taskData = {
      name,
      description,
      status: status || 'active',
      priority: priority || 'medium',
      target_likes: parseInt(target_likes) || 0,
      target_collects: parseInt(target_collects) || 0,
      target_comments: parseInt(target_comments) || 0
    };
    
    await Task.update(task.id, taskData);
    
    res.redirect('/tasks');
  } catch (error) {
    console.error('更新任务错误:', error);
    const task = await Task.getById(req.params.id);
    res.render('tasks/edit', { 
      user: req.user, 
      task, 
      error: '更新任务失败' 
    });
  }
});

// 删除任务
router.post('/tasks/delete/:id', requireLogin, async (req, res) => {
  try {
    const task = await Task.getById(req.params.id);
    
    if (!task || task.user_id !== req.user.id) {
      return res.redirect('/tasks');
    }
    
    // 先删除任务下的所有链接
    await Link.deleteAllByTaskId(task.id);
    
    // 然后删除任务
    await Task.delete(task.id);
    
    res.redirect('/tasks');
  } catch (error) {
    console.error('删除任务错误:', error);
    res.redirect('/tasks');
  }
});

// 任务详情页
router.get('/tasks/:id', requireLogin, async (req, res) => {
  try {
    const task = await Task.getById(req.params.id);
    
    if (!task || task.user_id !== req.user.id) {
      return res.redirect('/tasks');
    }
    
    const filters = {
      status: req.query.status,
      orderBy: req.query.orderBy || 'created_at',
      orderDir: req.query.orderDir || 'DESC',
      search: req.query.search || ''
    };
    
    // 分页参数
    const page = parseInt(req.query.page) || 1;
    const perPage = parseInt(req.query.perPage) || parseInt(res.locals.globalSettings?.['general.items_per_page']) || 20;
    const offset = (page - 1) * perPage;
    
    // 获取总链接数
    const totalLinks = await Link.countByTaskId(task.id, filters);
    const totalPages = Math.ceil(totalLinks / perPage);
    
    // 获取分页后的链接列表
    const links = await Link.getByTaskId(task.id, filters, perPage, offset);
    
    // 获取任务统计数据
    task.linkCount = await Task.getLinkCount(task.id);
    task.activeCount = await Task.getActiveLinkCount(task.id);
    task.completedCount = await Task.getCompletedLinkCount(task.id);
    task.errorCount = await Task.getErrorLinkCount(task.id);
    task.progress = await Task.getProgress(task.id);
    
    // 更新任务的实际操作数量（确保数据是最新的）
    await Task.updateActualCounts(task.id);
    
    // 重新获取更新后的任务数据
    const updatedTask = await Task.getById(req.params.id);
    task.actual_likes = updatedTask.actual_likes || 0;
    task.actual_collects = updatedTask.actual_collects || 0;
    
    // 生成分页URL的辅助函数
    const getPageUrl = (pageNum) => {
      const url = new URL(`${req.protocol}://${req.get('host')}${req.originalUrl}`);
      url.searchParams.set('page', pageNum);
      return url.pathname + url.search;
    };
    
    res.render('tasks/view', {
      user: req.user,
      task,
      links,
      filters,
      moment,
      currentPage: page,
      perPage,
      totalPages,
      getPageUrl,
      originalUrl: req.originalUrl
    });
  } catch (error) {
    console.error('任务详情错误:', error);
    res.render('error', { message: '加载任务详情失败' });
  }
});

// 任务操作
router.post('/tasks/action/:id', requireLogin, async (req, res) => {
  try {
    const task = await Task.getById(req.params.id);
    
    if (!task || task.user_id !== req.user.id) {
      return res.redirect('/tasks');
    }
    
    const { action } = req.body;
    
    switch (action) {
      case 'activate':
        await Link.activateTaskLinks(task.id);
        await Task.update(task.id, { status: 'active' });
        break;
      case 'pause':
        await Link.pauseTaskLinks(task.id);
        await Task.update(task.id, { status: 'paused' });
        break;
      case 'reset':
        await Link.resetTaskFailCount(task.id);
        break;
      case 'clear':
        // 清空任务中的所有链接
        await Link.deleteAllByTaskId(task.id);
        break;
      default:
        break;
    }
    
    res.redirect(`/tasks/${task.id}`);
  } catch (error) {
    console.error('任务操作错误:', error);
    res.redirect('/tasks');
  }
});

// 导入链接到任务
router.get('/tasks/:id/import', requireLogin, async (req, res) => {
  try {
    const task = await Task.getById(req.params.id);
    
    if (!task || task.user_id !== req.user.id) {
      return res.redirect('/tasks');
    }
    
    res.render('tasks/import', {
      user: req.user,
      task,
      error: null,
      success: null
    });
  } catch (error) {
    console.error('导入链接页面错误:', error);
    res.redirect(`/tasks/${req.params.id}`);
  }
});

// 处理导入链接到任务
router.post('/tasks/:id/import', requireLogin, async (req, res) => {
  try {
    const task = await Task.getById(req.params.id);
    
    if (!task || task.user_id !== req.user.id) {
      return res.redirect('/tasks');
    }
    
    const { 
      links, 
      skip_task_duplicates, 
      skip_other_duplicates,
      update_duplicates
    } = req.body;
    
    if (!links || links.trim() === '') {
      return res.render('tasks/import', {
        user: req.user,
        task,
        error: '请输入链接数据',
        success: null
      });
    }
    
    // 处理每行数据
    const rawLines = links.split('\n');
    const lines = rawLines.filter(line => line.trim() !== '');
    
    let successCount = 0;
    let taskDuplicateCount = 0;  // 当前任务中的重复
    let otherDuplicateCount = 0;  // 其他任务中的重复
    let updatedCount = 0;  // 更新的链接数量
    let errorCount = 0;
    
    // 逐行处理
    for (let i = 0; i < lines.length; i++) {
      try {
        const line = lines[i].trim();
        
        // 移除开头的@符号（如果有）
        let cleanLine = line;
        if (cleanLine.startsWith('@')) {
          cleanLine = cleanLine.substring(1);
        }
        
        // 解析链接和目标数据
        // 格式: URL [点赞数] [收藏数] [评论数]
        // 如果是 "-" 则表示没有提供该数据
        const parts = cleanLine.split(' ').filter(p => p.trim());
        const url = parts[0];
        
        // 验证URL
        if (!url || !(url.includes('xiaohongshu.com') || url.includes('xhslink.com'))) {
          errorCount++;
          continue;
        }
        
        // 解析目标数据
        let target_likes = task.target_likes || 0;
        let target_collects = task.target_collects || 0;
        let target_comments = task.target_comments || 0;
        
        if (parts.length > 1) {
          // 只有当使用"-"站位符时才使用任务组的默认值
          // 如果明确设置为0，则使用0
          if (parts[1] === '-') {
            target_likes = task.target_likes || 0;
          } else {
            target_likes = parseInt(parts[1]);
            if (isNaN(target_likes)) target_likes = task.target_likes || 0;
          }
        }
        
        if (parts.length > 2) {
          // 只有当使用"-"站位符时才使用任务组的默认值
          // 如果明确设置为0，则使用0
          if (parts[2] === '-') {
            target_collects = task.target_collects || 0;
          } else {
            target_collects = parseInt(parts[2]);
            if (isNaN(target_collects)) target_collects = task.target_collects || 0;
          }
        }
        
        if (parts.length > 3) {
          // 只有当使用"-"站位符时才使用任务组的默认值
          // 如果明确设置为0，则使用0
          if (parts[3] === '-') {
            target_comments = task.target_comments || 0;
          } else {
            target_comments = parseInt(parts[3]);
            if (isNaN(target_comments)) target_comments = task.target_comments || 0;
          }
        }
        
        // 检查链接是否在当前任务中已存在
        const existingLinkInTask = await Link.checkExistsInTask(req.user.id, url, task.id);
        
        // 如果链接在当前任务中已存在
        if (existingLinkInTask) {
          taskDuplicateCount++;
          
          // 如果选择了更新重复链接的数据，则更新
          if (update_duplicates === 'on') {
            console.log(`更新重复链接 ${url} 的目标数据:`, {
              target_likes,
              target_collects,
              target_comments,
              like_target: target_likes,
              collect_target: target_collects,
              comment_target: target_comments
            });
            
            await Link.update(existingLinkInTask.id, {
              target_likes: target_likes,
              target_collects: target_collects,
              target_comments: target_comments,
              like_target: target_likes,
              collect_target: target_collects,
              comment_target: target_comments
            });
            updatedCount++;
            continue;
          }
          
          // 如果选择了跳过当前任务中的重复链接，则继续处理下一条
          if (skip_task_duplicates === 'on') {
            continue;
          }
          
          continue;
        }
        
        // 只有当选择了"跳过其他任务中的重复链接"时，才检查链接是否在其他任务中已存在
        if (skip_other_duplicates === 'on') {
          // 检查链接是否在其他任务中已存在
          const existingLink = await Link.checkExists(req.user.id, url);
          
          if (existingLink && existingLink.task_id !== task.id) {
            otherDuplicateCount++;
            continue; // 跳过这个链接，继续处理下一个
          }
        }
        
        // 创建新链接
        const linkData = {
          user_id: req.user.id,
          task_id: task.id,
          url: url,
          original_likes: 0,
          original_collects: 0,
          original_comments: 0,
          current_likes: 0,
          current_collects: 0,
          current_comments: 0,
          target_likes: target_likes,
          target_collects: target_collects,
          target_comments: target_comments,
          like_target: target_likes,
          collect_target: target_collects,
          comment_target: target_comments,
          priority: task.priority || 'medium',
          status: 'active'
        };
        
        await Link.create(linkData);
        successCount++;
      } catch (err) {
        console.error(`导入链接错误 (${lines[i]}):`, err);
        errorCount++;
      }
    }
    
    if (successCount === 0 && updatedCount === 0) {
      let errorMsg = '没有成功导入或更新任何链接';
      if (taskDuplicateCount > 0) {
        errorMsg += `，发现 ${taskDuplicateCount} 个当前任务中的重复链接`;
      }
      if (otherDuplicateCount > 0) {
        errorMsg += `，发现 ${otherDuplicateCount} 个其他任务中的重复链接`;
      }
      
      return res.render('tasks/import', {
        user: req.user,
        task,
        error: errorMsg,
        success: null
      });
    }
    
    let message = '';
    if (successCount > 0) {
      message = `成功导入 ${successCount} 个链接到任务 "${task.name}"`;
    }
    
    if (updatedCount > 0) {
      if (message) {
        message += `，更新了 ${updatedCount} 个重复链接`;
      } else {
        message = `成功更新了 ${updatedCount} 个重复链接`;
      }
    }
    
    if (taskDuplicateCount > 0 && skip_task_duplicates === 'on') {
      message += `，跳过了 ${taskDuplicateCount} 个当前任务中的重复链接`;
    }
    if (otherDuplicateCount > 0 && skip_other_duplicates === 'on') {
      message += `，跳过了 ${otherDuplicateCount} 个其他任务中的重复链接`;
    }
    
    return res.render('tasks/import', {
      user: req.user,
      task,
      error: null,
      success: message
    });
  } catch (error) {
    console.error('导入链接到任务错误:', error);
    res.render('tasks/import', {
      user: req.user,
      task: await Task.getById(req.params.id),
      error: '导入链接失败: ' + error.message,
      success: null
    });
  }
});

// 设置页面
router.get('/settings', requireLogin, async (req, res) => {
  try {
    // 获取所有设置
    const settings = await Setting.getAll(req.user.id);

    // 将设置转换为对象格式，方便在模板中使用
    const settingsMap = {};

    // 确保settings是一个数组再使用forEach
    if (settings && Array.isArray(settings)) {
      settings.forEach(setting => {
        settingsMap[setting.setting_key] = setting.setting_value;
      });
    }

    // 获取默认设置，用于填充未设置的选项
    const defaultSettings = Setting.getDefaultSettings();

    // 合并用户设置和默认设置
    const mergedSettings = { ...defaultSettings, ...settingsMap };



    res.render('settings/index', {
      user: req.user,
      settings: mergedSettings,
      success: req.query.success ? '设置已保存' : null,
      error: null
    });
  } catch (error) {
    console.error('加载设置页面错误:', error);
    res.render('error', { message: '加载设置页面失败' });
  }
});

// 保存常规设置
router.post('/settings/general', requireLogin, async (req, res) => {
  try {
    const { app_name, items_per_page } = req.body;
    
    // 验证数据
    if (!app_name) {
      return res.render('settings/index', {
        user: req.user,
        settings: req.body,
        success: null,
        error: '应用名称不能为空'
      });
    }
    
    // 保存设置
    await Setting.set(req.user.id, 'general.app_name', app_name, '应用名称');
    await Setting.set(req.user.id, 'general.items_per_page', items_per_page || '20', '每页显示条目数');
    
    res.redirect('/settings?success=true');
  } catch (error) {
    console.error('保存常规设置错误:', error);
    res.render('error', { message: '保存设置失败' });
  }
});

// 保存任务设置
router.post('/settings/task', requireLogin, async (req, res) => {
  try {
    console.log('收到任务设置保存请求:', req.body);
    
    const { 
      default_priority, 
      default_target_likes, 
      default_target_collects, 
      default_target_comments 
    } = req.body;
    
    // 保存设置
    console.log('开始保存任务设置...');
    
    try {
      await Setting.set(req.user.id, 'task.default_priority', default_priority || 'medium', '默认任务优先级');
      console.log('已保存: task.default_priority =', default_priority || 'medium');
    } catch (error) {
      console.error('保存task.default_priority失败:', error);
      throw error;
    }
    
    try {
      await Setting.set(req.user.id, 'task.default_target_likes', default_target_likes || '100', '默认目标点赞数');
      console.log('已保存: task.default_target_likes =', default_target_likes || '100');
    } catch (error) {
      console.error('保存task.default_target_likes失败:', error);
      throw error;
    }
    
    try {
      await Setting.set(req.user.id, 'task.default_target_collects', default_target_collects || '50', '默认目标收藏数');
      console.log('已保存: task.default_target_collects =', default_target_collects || '50');
    } catch (error) {
      console.error('保存task.default_target_collects失败:', error);
      throw error;
    }
    
    try {
      await Setting.set(req.user.id, 'task.default_target_comments', default_target_comments || '0', '默认目标评论数');
      console.log('已保存: task.default_target_comments =', default_target_comments || '0');
    } catch (error) {
      console.error('保存task.default_target_comments失败:', error);
      throw error;
    }
    
    console.log('所有任务设置已保存成功');
    res.redirect('/settings?success=true');
  } catch (error) {
    console.error('保存任务设置错误:', error);
    
    // 获取所有设置以便重新渲染页面
    try {
      const settings = await Setting.getAll(req.user.id);
      const settingsMap = {};
      
      if (settings && Array.isArray(settings)) {
        settings.forEach(setting => {
          settingsMap[setting.setting_key] = setting.setting_value;
        });
      }
      
      const defaultSettings = Setting.getDefaultSettings();
      const mergedSettings = { ...defaultSettings, ...settingsMap, ...req.body };
      
      res.render('settings/index', {
        user: req.user,
        settings: mergedSettings,
        success: null,
        error: '保存设置失败: ' + error.message
      });
    } catch (settingsError) {
      console.error('获取设置错误:', settingsError);
      res.render('error', { message: '保存设置失败: ' + error.message });
    }
  }
});

// 添加GET处理程序，将/settings/task重定向到/settings
router.get('/settings/task', requireLogin, async (req, res) => {
  res.redirect('/settings');
});

// 性能优化设置页面
router.get('/optimization-settings', requireLogin, async (req, res) => {
  try {
    const settings = await Setting.getAll(req.user.id);
    const settingsObj = {};
    settings.forEach(setting => {
      settingsObj[setting.key] = setting.value;
    });

    res.render('optimization_settings', {
      settings: settingsObj,
      user: req.user
    });
  } catch (error) {
    console.error('获取优化设置页面错误:', error);
    res.status(500).send('服务器错误');
  }
});

// 保存性能优化设置
router.post('/optimization-settings', requireLogin, async (req, res) => {
  try {
    const userId = req.user.id;
    const settings = req.body;

    // 保存设置
    for (const [key, value] of Object.entries(settings)) {
      await Setting.set(userId, key, value);
    }

    res.json({ success: true, message: '设置保存成功' });
  } catch (error) {
    console.error('保存优化设置错误:', error);
    res.json({ success: false, message: '保存失败: ' + error.message });
  }
});

// 添加通用重定向处理程序，捕获所有settings相关的错误路由
router.get('/settings/*', requireLogin, async (req, res) => {
  console.log('捕获到错误的settings路由:', req.path);
  res.redirect('/settings');
});

// 保存设备设置
router.post('/settings/device', requireLogin, async (req, res) => {
  try {
    const { 
      auto_assign, 
      max_operations_per_day, 
      trust_threshold 
    } = req.body;
    
    // 保存设置
    await Setting.set(req.user.id, 'device.auto_assign', auto_assign === 'on' ? 'true' : 'false', '自动分配任务给设备');
    await Setting.set(req.user.id, 'device.max_operations_per_day', max_operations_per_day || '100', '每个设备每日最大操作次数');
    await Setting.set(req.user.id, 'device.trust_threshold', trust_threshold || '70', '设备信任度阈值');
    
    res.redirect('/settings?success=true');
  } catch (error) {
    console.error('保存设备设置错误:', error);
    res.render('error', { message: '保存设置失败' });
  }
});

// 保存链接操作设置
router.post('/settings/link', requireLogin, async (req, res) => {
  try {
    const {
      allocation_mode,
      round_robin_reset_hours,
      priority_strict_mode,
      max_concurrent_operations,
      cooldown_seconds,
      processing_timeout_seconds,
      max_retries
    } = req.body;

    // 保存链接分配策略设置
    await Setting.set(req.user.id, 'link.allocation_mode', allocation_mode || 'smart', '链接分配模式');
    await Setting.set(req.user.id, 'link.round_robin_reset_hours', round_robin_reset_hours || '24', '循环计数器重置间隔（小时）');
    await Setting.set(req.user.id, 'link.priority_strict_mode', priority_strict_mode ? 'true' : 'false', '严格优先级模式');

    // 保存其他设置
    await Setting.set(req.user.id, 'link.max_concurrent_operations', max_concurrent_operations || '3', '同一链接最大并发操作数');
    await Setting.set(req.user.id, 'link.cooldown_seconds', cooldown_seconds || '300', '链接操作后冷却时间（秒）');
    await Setting.set(req.user.id, 'link.processing_timeout_seconds', processing_timeout_seconds || '120', '链接处理超时时间（秒）');
    await Setting.set(req.user.id, 'link.max_retries', max_retries || '3', '链接操作失败最大重试次数');

    console.log(`用户(${req.user.id})保存链接操作设置成功 - 分配模式: ${allocation_mode}`);
    res.redirect('/settings?success=true#link');
  } catch (error) {
    console.error('保存链接操作设置错误:', error);
    res.render('error', { message: '保存设置失败' });
  }
});

// 保存系统性能设置
router.post('/settings/system', requireLogin, async (req, res) => {
  try {
    const {
      max_concurrent_requests,
      request_rate_limit,
      queue_timeout_seconds,
      // 新增的优化设置
      'link.use_optimization': useOptimization,
      'optimization.use_cache': useCache,
      'optimization.use_queue': useQueue
    } = req.body;

    console.log('接收到的表单数据:', req.body);

    // 保存原有的系统设置
    await Setting.set(req.user.id, 'system.max_concurrent_requests', max_concurrent_requests || '20', '最大并发请求数');
    await Setting.set(req.user.id, 'system.request_rate_limit', request_rate_limit || '60', '每分钟最大请求次数');
    await Setting.set(req.user.id, 'system.queue_timeout_seconds', queue_timeout_seconds || '30', '请求队列超时时间（秒）');

    // 保存新增的优化设置
    await Setting.set(req.user.id, 'link.use_optimization', useOptimization === 'on' ? 'true' : 'false', '启用链接分发性能优化');
    await Setting.set(req.user.id, 'optimization.use_cache', useCache === 'on' ? 'true' : 'false', '启用链接缓存');
    await Setting.set(req.user.id, 'optimization.use_queue', useQueue === 'on' ? 'true' : 'false', '启用请求队列处理');

    console.log('优化设置已保存:', {
      useOptimization: useOptimization === 'on' ? 'true' : 'false',
      useCache: useCache === 'on' ? 'true' : 'false',
      useQueue: useQueue === 'on' ? 'true' : 'false'
    });

    res.redirect('/settings?success=true');
  } catch (error) {
    console.error('保存系统性能设置错误:', error);
    res.render('error', { message: '保存设置失败' });
  }
});

// 保存通知设置
router.post('/settings/notification', requireLogin, async (req, res) => {
  try {
    const { 
      email_enabled, 
      email, 
      task_completion, 
      device_offline, 
      error_threshold 
    } = req.body;
    
    // 验证数据
    if (email_enabled === 'on' && !email) {
      return res.render('settings/index', {
        user: req.user,
        settings: req.body,
        success: null,
        error: '启用邮件通知时，邮箱地址不能为空'
      });
    }
    
    // 保存设置
    await Setting.set(req.user.id, 'notification.email_enabled', email_enabled === 'on' ? 'true' : 'false', '启用邮件通知');
    await Setting.set(req.user.id, 'notification.email', email || '', '通知邮箱');
    await Setting.set(req.user.id, 'notification.task_completion', task_completion === 'on' ? 'true' : 'false', '任务完成通知');
    await Setting.set(req.user.id, 'notification.device_offline', device_offline === 'on' ? 'true' : 'false', '设备离线通知');
    await Setting.set(req.user.id, 'notification.error_threshold', error_threshold || '10', '错误阈值通知');
    
    res.redirect('/settings?success=true');
  } catch (error) {
    console.error('保存通知设置错误:', error);
    res.render('error', { message: '保存设置失败' });
  }
});

// 监控面板路由
router.get('/monitor', requireLogin, async (req, res) => {
  try {
    res.render('monitor', { 
      title: '实时监控',
      user: req.user
    });
  } catch (error) {
    console.error('获取监控页面出错:', error);
    res.render('error', { message: '获取监控页面失败' });
  }
});

// 统计数据路由
router.get('/stats/detailed', requireLogin, async (req, res) => {
  try {
    const userId = req.user.id;
    const { startDate, endDate, taskId, deviceId, status, operationType } = req.query;
    const page = parseInt(req.query.page) || 1;
    const limit = 50;
    const offset = (page - 1) * limit;
    
    // 默认查询今天和过去30天的数据
    const defaultEndDate = moment().format('YYYY-MM-DD');
    const defaultStartDate = moment().subtract(30, 'days').format('YYYY-MM-DD');
    
    // 构建查询条件
    const queryParams = [userId];
    let queryConditions = "WHERE user_id = ?";
    
    if (startDate) {
      queryConditions += " AND DATE(created_at) >= ?";
      queryParams.push(startDate);
    } else {
      queryConditions += " AND DATE(created_at) >= ?";
      queryParams.push(defaultStartDate);
    }
    
    if (endDate) {
      queryConditions += " AND DATE(created_at) <= ?";
      queryParams.push(endDate);
    } else {
      queryConditions += " AND DATE(created_at) <= ?";
      queryParams.push(defaultEndDate);
    }
    
    if (taskId) {
      queryConditions += " AND task_id = ?";
      queryParams.push(taskId);
    }
    
    if (deviceId) {
      queryConditions += " AND device_id = ?";
      queryParams.push(deviceId);
    }
    
    if (status) {
      queryConditions += " AND status = ?";
      queryParams.push(status);
    }
    
    if (operationType) {
      queryConditions += " AND operation_type = ?";
      queryParams.push(operationType);
    }
    
    // 统计摘要数据
    const summaryQuery = `
      SELECT 
        COUNT(*) as totalOperations,
        SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as successOperations,
        SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failedOperations,
        SUM(CASE WHEN operation_type = 'like' THEN 1 ELSE 0 END) as likeOperations,
        SUM(CASE WHEN operation_type = 'collect' THEN 1 ELSE 0 END) as collectOperations,
        SUM(CASE WHEN operation_type = 'like' AND status = 'success' THEN like_count ELSE 0 END) as totalLikes,
        SUM(CASE WHEN operation_type = 'collect' AND status = 'success' THEN collect_count ELSE 0 END) as totalCollects
      FROM operation_logs ${queryConditions}
    `;
    
    // 每日统计数据
    const dailyStatsQuery = `
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as total,
        SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as success,
        SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed
      FROM operation_logs ${queryConditions}
      GROUP BY DATE(created_at)
      ORDER BY date
    `;
    
    // 设备统计数据
    const deviceStatsQuery = `
      SELECT 
        ol.device_id,
        d.device_name,
        COUNT(*) as total,
        SUM(CASE WHEN ol.status = 'success' THEN 1 ELSE 0 END) as success,
        SUM(CASE WHEN ol.status = 'failed' THEN 1 ELSE 0 END) as failed
      FROM operation_logs ol
      LEFT JOIN devices d ON ol.device_id = d.id
      ${queryConditions}
      GROUP BY ol.device_id
      ORDER BY total DESC
    `;
    
    // 操作日志
    const logsQuery = `
      SELECT 
        ol.*,
        d.device_name,
        t.name as task_name,
        l.url
      FROM operation_logs ol
      LEFT JOIN devices d ON ol.device_id = d.id
      LEFT JOIN tasks t ON ol.task_id = t.id
      LEFT JOIN links l ON ol.link_id = l.id
      ${queryConditions}
      ORDER BY ol.created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    // 计算总记录数用于分页
    const countQuery = `SELECT COUNT(*) as total FROM operation_logs ${queryConditions}`;
    
    // 获取所有任务和设备用于筛选
    const tasks = await Task.getByUserId(userId);
    const devices = await Device.getByUserId(userId);
    
    // 执行查询
    const [summary] = await db.query(summaryQuery, queryParams);
    const dailyStats = await db.query(dailyStatsQuery, queryParams);
    const deviceStats = await db.query(deviceStatsQuery, queryParams);
    
    const logsQueryParams = [...queryParams, limit, offset];
    const logs = await db.query(logsQuery, logsQueryParams);
    
    const [countResult] = await db.query(countQuery, queryParams);
    const totalCount = countResult.total;
    const totalPages = Math.ceil(totalCount / limit);
    
    // CSV导出功能
    if (req.query.export === 'csv') {
      // 不分页的日志查询
      const exportQuery = `
        SELECT 
          ol.created_at,
          d.device_name,
          t.name as task_name,
          l.url,
          ol.operation_type,
          ol.status,
          ol.like_count,
          ol.collect_count,
          ol.message
        FROM operation_logs ol
        LEFT JOIN devices d ON ol.device_id = d.id
        LEFT JOIN tasks t ON ol.task_id = t.id
        LEFT JOIN links l ON ol.link_id = l.id
        ${queryConditions}
        ORDER BY ol.created_at DESC
      `;
      
      const exportData = await db.query(exportQuery, queryParams);
      
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename=operations-${moment().format('YYYY-MM-DD')}.csv`);
      
      let csv = '时间,设备,任务,链接,操作类型,状态,点赞数,收藏数,消息\n';
      
      exportData.forEach(row => {
        const formatDate = moment(row.created_at).format('YYYY-MM-DD HH:mm:ss');
        const formatOperationType = row.operation_type === 'like' ? '点赞' : '收藏';
        const formatStatus = row.status === 'success' ? '成功' : '失败';
        
        csv += `"${formatDate}","${row.device_name || '未知设备'}","${row.task_name || '未知任务'}","${row.url || ''}","${formatOperationType}","${formatStatus}","${row.like_count || 0}","${row.collect_count || 0}","${row.message || ''}"\n`;
      });
      
      return res.send(csv);
    }
    
    // 计算成功率
    const successRate = summary.totalOperations > 0 
      ? (summary.successOperations / summary.totalOperations * 100) 
      : 0;
    
    // 辅助函数用于生成分页URL
    const getPaginationUrl = (pageNum) => {
      const params = new URLSearchParams(req.query);
      params.set('page', pageNum);
      return `${req.path}?${params.toString()}`;
    };
    
    res.render('stats/detailed', {
      title: '详细数据统计',
      user: req.user,
      tasks,
      devices,
      logs,
      summary: {
        ...summary,
        successRate
      },
      dailyStats,
      deviceStats,
      startDate: startDate || defaultStartDate,
      endDate: endDate || defaultEndDate,
      taskId: taskId || '',
      deviceId: deviceId || '',
      status: status || '',
      operationType: operationType || '',
      currentPage: page,
      totalPages,
      getPaginationUrl,
      moment
    });
  } catch (error) {
    console.error('获取详细统计数据出错:', error);
    res.render('error', { message: '获取统计数据失败' });
  }
});

// 任务进度页面
router.get('/tasks/progress/:id', requireLogin, async (req, res) => {
  try {
    const taskId = req.params.id;
    const userId = req.user.id;
    
    // 获取任务基本信息
    const task = await Task.getById(taskId);
    
    // 验证任务是否存在且属于当前用户
    if (!task || task.user_id !== userId) {
      return res.redirect('/tasks');
    }
    
    // 获取链接状态统计
    const linkStatusQuery = `
      SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
        SUM(CASE WHEN status = 'processing' THEN 1 ELSE 0 END) as processing,
        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as waiting,
        SUM(CASE WHEN status = 'error' THEN 1 ELSE 0 END) as failed,
        SUM(CASE WHEN status = 'timeout' THEN 1 ELSE 0 END) as timeout
      FROM links
      WHERE task_id = ?
    `;
    
    const [linkStatus] = await db.query(linkStatusQuery, [taskId]);
    
    // 计算进度百分比
    const percentage = linkStatus.total > 0 
      ? (linkStatus.completed / linkStatus.total * 100) 
      : 0;
    
    // 获取操作结果统计
    const operationResultsQuery = `
      SELECT 
        SUM(CASE WHEN status = 'success' AND like_count > 0 THEN 1 ELSE 0 END) as likeSuccess,
        SUM(CASE WHEN status = 'success' AND collect_count > 0 THEN 1 ELSE 0 END) as collectSuccess,
        SUM(CASE WHEN status = 'failed' AND like_count > 0 THEN 1 ELSE 0 END) as likeFailed,
        SUM(CASE WHEN status = 'failed' AND collect_count > 0 THEN 1 ELSE 0 END) as collectFailed,
        SUM(like_count) as totalLikes,
        SUM(collect_count) as totalCollects
      FROM operation_logs
      WHERE task_id = ?
    `;
    
    const [operationResults] = await db.query(operationResultsQuery, [taskId]);
    
    // 获取近期操作日志
    const recentLogsQuery = `
      SELECT 
        ol.*,
        d.device_name,
        l.url
      FROM operation_logs ol
      LEFT JOIN devices d ON ol.device_id = d.id
      LEFT JOIN links l ON ol.link_id = l.id
      WHERE ol.task_id = ?
      ORDER BY ol.created_at DESC
      LIMIT 20
    `;
    
    const recentLogs = await db.query(recentLogsQuery, [taskId]);
    
    // 获取参与该任务的设备信息
    const devicesQuery = `
      SELECT 
        d.id,
        d.device_name,
        d.last_active_time,
        CASE WHEN TIMESTAMPDIFF(MINUTE, d.last_active_time, NOW()) <= 5 THEN 1 ELSE 0 END as is_online,
        COUNT(DISTINCT ol.id) as total_operations,
        SUM(CASE WHEN ol.status = 'success' THEN 1 ELSE 0 END) as success_count,
        SUM(CASE WHEN ol.status = 'failed' THEN 1 ELSE 0 END) as failed_count
      FROM devices d
      LEFT JOIN operation_logs ol ON d.id = ol.device_id AND ol.task_id = ?
      WHERE d.user_id = ?
      GROUP BY d.id
      HAVING total_operations > 0
      ORDER BY is_online DESC, total_operations DESC
    `;
    
    const devices = await db.query(devicesQuery, [taskId, userId]);
    
    res.render('tasks/progress', {
      title: '任务进度详情',
      user: req.user,
      task,
      progressData: {
        ...linkStatus,
        percentage
      },
      operationResults: {
        likeSuccess: operationResults.likeSuccess || 0,
        collectSuccess: operationResults.collectSuccess || 0,
        likeFailed: operationResults.likeFailed || 0,
        collectFailed: operationResults.collectFailed || 0,
        totalLikes: operationResults.totalLikes || 0,
        totalCollects: operationResults.totalCollects || 0
      },
      recentLogs,
      devices,
      moment
    });
  } catch (error) {
    console.error('获取任务进度页面出错:', error);
    res.render('error', { message: '获取任务进度失败' });
  }
});

// 设备配置页面
router.get('/device-configs', requireLogin, async (req, res) => {
  try {
    console.log('访问设备配置页面 - 用户ID:', req.user.id);
    
    // 获取设备配置列表
    const configs = await DeviceConfig.getAllByUserId(req.user.id);
    console.log('找到配置数量:', configs ? configs.length : 0);
    
    // 确保configs是数组
    const configList = Array.isArray(configs) ? configs : [];
    
    // 处理配置中的JSON数据
    configList.forEach(config => {
      if (typeof config.config === 'string') {
        try {
          config.config = JSON.parse(config.config);
        } catch (e) {
          console.error('解析配置JSON失败:', e);
          config.config = {};
        }
      }
    });
    
    res.render('device_configs/index', {
      title: '设备配置管理',
      user: req.user,
      configs: configList,
      success: req.query.success,
      error: req.query.error,
      moment
    });
  } catch (error) {
    console.error('获取设备配置列表错误:', error);
    res.render('error', { message: '加载设备配置页面失败: ' + error.message });
  }
});

// 创建设备配置页面
router.get('/device-configs/create', requireLogin, (req, res) => {
  res.render('device_configs/edit', {
    user: req.user,
    config: {
      id: null,
      name: '',
      config: '{}',
      description: '',
      is_default: false
    },
    isNew: true,
    error: null,
    active: 'device-configs' // 添加active参数
  });
});

// 处理创建设备配置
router.post('/device-configs/create', requireLogin, async (req, res) => {
  try {
    const { name, config, description, is_default } = req.body;
    let configObj = {};
    
    try {
      configObj = JSON.parse(config);
    } catch (e) {
      return res.render('device_configs/edit', {
        user: req.user,
        config: {
          id: null,
          name,
          config,
          description,
          is_default: is_default === 'true'
        },
        isNew: true,
        error: 'JSON格式无效',
        active: 'device-configs' // 添加active参数
      });
    }
    
    await DeviceConfig.create({
      user_id: req.user.id,
      name,
      config: configObj,
      description,
      is_default: is_default === 'true'
    });
    
    res.redirect('/device-configs');
  } catch (error) {
    console.error('创建设备配置错误:', error);
    res.render('device_configs/edit', {
      user: req.user,
      config: req.body,
      isNew: true,
      error: '创建设备配置失败: ' + error.message,
      active: 'device-configs' // 添加active参数
    });
  }
});

// 编辑设备配置页面
router.get('/device-configs/edit/:id', requireLogin, async (req, res) => {
  try {
    const configId = req.params.id;
    const config = await DeviceConfig.getById(configId, req.user.id);
    
    if (!config) {
      return res.render('error', { message: '设备配置不存在或无权限编辑' });
    }
    
    // 将配置转换为格式化的JSON字符串
    config.config = JSON.stringify(config.config, null, 2);
    
    res.render('device_configs/edit', {
      user: req.user,
      config,
      isNew: false,
      error: null,
      active: 'device-configs' // 添加active参数
    });
  } catch (error) {
    console.error('获取设备配置错误:', error);
    res.render('error', { message: '加载设备配置数据失败' });
  }
});

// 处理编辑设备配置
router.post('/device-configs/edit/:id', requireLogin, async (req, res) => {
  try {
    const configId = req.params.id;
    const config = await DeviceConfig.getById(configId, req.user.id);
    
    if (!config) {
      return res.render('error', { message: '设备配置不存在或无权限编辑' });
    }
    
    const { name, config: configJson, description, is_default } = req.body;
    let configObj = {};
    
    try {
      configObj = JSON.parse(configJson);
    } catch (e) {
      return res.render('device_configs/edit', {
        user: req.user,
        config: {
          id: configId,
          name,
          config: configJson,
          description,
          is_default: is_default === 'true'
        },
        isNew: false,
        error: 'JSON格式无效'
      });
    }
    
    await DeviceConfig.update(configId, req.user.id, {
      name,
      config: configObj,
      description,
      is_default: is_default === 'true'
    });
    
    res.redirect('/device-configs');
  } catch (error) {
    console.error('更新设备配置错误:', error);
    res.render('device_configs/edit', {
      user: req.user,
      config: {
        id: req.params.id,
        ...req.body,
        is_default: req.body.is_default === 'true'
      },
      isNew: false,
      error: '更新设备配置失败: ' + error.message
    });
  }
});

// 删除设备配置
router.post('/device-configs/delete/:id', requireLogin, async (req, res) => {
  try {
    const configId = req.params.id;
    await DeviceConfig.delete(configId, req.user.id);
    res.redirect('/device-configs');
  } catch (error) {
    console.error('删除设备配置错误:', error);
    res.render('error', { message: '删除设备配置失败: ' + error.message });
  }
});

// 设置默认配置
router.post('/device-configs/set-default/:id', requireLogin, async (req, res) => {
  try {
    const configId = req.params.id;
    const config = await DeviceConfig.getById(configId, req.user.id);
    
    if (!config) {
      return res.render('error', { message: '设备配置不存在或无权限设置' });
    }
    
    await DeviceConfig.update(configId, req.user.id, {
      name: config.name,
      config: JSON.parse(config.config),
      description: config.description,
      is_default: true
    });
    
    res.redirect('/device-configs');
  } catch (error) {
    console.error('设置默认配置错误:', error);
    res.render('error', { message: '设置默认配置失败: ' + error.message });
  }
});

// 设备配置API路由 - 直接在web路由中处理
router.get('/api/device-configs', requireLogin, async (req, res) => {
  try {
    console.log('获取所有设备配置 - 用户ID:', req.user.id);
    const configs = await DeviceConfig.getAllByUserId(req.user.id);
    console.log('找到配置数量:', configs.length);
    res.json({
      success: true,
      configs
    });
  } catch (error) {
    console.error('获取配置列表错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

router.post('/api/device-configs', requireLogin, async (req, res) => {
  try {
    console.log('创建设备配置 - 用户ID:', req.user.id);
    console.log('请求数据:', JSON.stringify(req.body));
    
    const { name, description, config, is_default } = req.body;
    
    if (!name || !config) {
      return res.status(400).json({
        success: false,
        message: '配置名称和内容不能为空'
      });
    }
    
    // 验证JSON格式
    let configObj;
    try {
      configObj = typeof config === 'string' ? JSON.parse(config) : config;
      // 验证configObj是否是有效的对象
      if (!configObj || typeof configObj !== 'object' || Array.isArray(configObj)) {
        throw new Error('配置必须是有效的JSON对象');
      }
    } catch (e) {
      console.error('JSON格式验证错误:', e);
      return res.status(400).json({
        success: false,
        message: 'JSON格式错误: ' + e.message
      });
    }
    
    const id = await DeviceConfig.create({
      user_id: req.user.id,
      name,
      description,
      config: configObj,
      is_default: is_default || false
    });
    
    console.log('配置创建成功 - ID:', id);
    res.status(201).json({
      success: true,
      message: '配置创建成功',
      id
    });
  } catch (error) {
    console.error('创建配置错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误: ' + error.message
    });
  }
});

router.put('/api/device-configs/:id', requireLogin, async (req, res) => {
  try {
    const id = req.params.id;
    console.log('更新设备配置 - ID:', id, '用户ID:', req.user.id);
    console.log('请求数据:', JSON.stringify(req.body));
    
    const { name, description, config, is_default } = req.body;
    
    if (!name || !config) {
      return res.status(400).json({
        success: false,
        message: '配置名称和内容不能为空'
      });
    }
    
    const existingConfig = await DeviceConfig.getById(id, req.user.id);
    
    if (!existingConfig) {
      return res.status(404).json({
        success: false,
        message: '配置不存在或无权限修改'
      });
    }
    
    // 验证JSON格式
    let configObj;
    try {
      configObj = typeof config === 'string' ? JSON.parse(config) : config;
      // 验证configObj是否是有效的对象
      if (!configObj || typeof configObj !== 'object' || Array.isArray(configObj)) {
        throw new Error('配置必须是有效的JSON对象');
      }
    } catch (e) {
      console.error('JSON格式验证错误:', e);
      return res.status(400).json({
        success: false,
        message: 'JSON格式错误: ' + e.message
      });
    }
    
    await DeviceConfig.update(id, req.user.id, {
      name,
      description,
      config: configObj,
      is_default: is_default || false
    });
    
    console.log('配置更新成功 - ID:', id);
    res.json({
      success: true,
      message: '配置更新成功'
    });
  } catch (error) {
    console.error('更新配置错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误: ' + error.message
    });
  }
});

router.delete('/api/device-configs/:id', requireLogin, async (req, res) => {
  try {
    const id = req.params.id;
    console.log('删除设备配置 - ID:', id, '用户ID:', req.user.id);
    
    // 先检查配置是否存在
    const config = await DeviceConfig.getById(id, req.user.id);
    if (!config) {
      console.log('配置不存在或无权限删除');
      return res.status(404).json({
        success: false,
        message: '配置不存在或无权限删除'
      });
    }
    
    // 执行删除操作
    const result = await DeviceConfig.delete(id, req.user.id);
    
    if (!result) {
      console.log('删除失败 - 配置不存在或已删除');
      return res.status(404).json({
        success: false,
        message: '配置不存在或已删除'
      });
    }
    
    console.log('配置删除成功 - ID:', id);
    res.json({
      success: true,
      message: '配置已删除'
    });
  } catch (error) {
    console.error('删除配置错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误: ' + error.message
    });
  }
});

// 设备配置测试页面
router.get('/device-configs/test', requireLogin, async (req, res) => {
  try {
    res.render('device_configs/test', {
      title: '设备配置功能测试',
      user: req.user
    });
  } catch (error) {
    console.error('加载设备配置测试页面错误:', error);
    res.render('error', { message: '加载测试页面失败: ' + error.message });
  }
});

// 导出链接列表为CSV
router.get('/links/export', requireLogin, async (req, res) => {
  try {
    const filters = {
      status: req.query.status,
      priority: req.query.priority,
      batch: req.query.batch,
      task_id: req.query.task_id ? parseInt(req.query.task_id) : null,
      failCount: req.query.failCount ? parseInt(req.query.failCount) : null,
      dateFrom: req.query.dateFrom,
      dateTo: req.query.dateTo,
      orderBy: req.query.orderBy || 'created_at',
      orderDir: req.query.orderDir || 'DESC',
      search: req.query.search || ''
    };
    
    // 获取所有链接（不分页）
    const links = await Link.getAllByUserId(req.user.id, filters);
    
    // 获取所有任务，用于填充任务名称
    const tasks = await Task.getAllByUserId(req.user.id);
    
    // 为每个链接添加任务名称
    for (const link of links) {
      if (link.task_id) {
        const task = tasks.find(t => t.id === link.task_id);
        if (task) {
          link.task_name = task.name;
        }
      }
    }
    
    // 设置响应头，指定文件类型和下载文件名
    res.setHeader('Content-Type', 'text/csv; charset=utf-8');
    res.setHeader('Content-Disposition', `attachment; filename=links-export-${moment().format('YYYY-MM-DD-HH-mm-ss')}.csv`);
    
    // CSV表头
    let csv = 'ID,链接地址,初始点赞,当前点赞,增加点赞,初始收藏,当前收藏,增加收藏,点赞目标,收藏目标,失败次数,优先级,状态,任务名称,创建时间\n';
    
    // 添加数据行
    for (const link of links) {
      const increasedLikes = link.current_likes - link.original_likes;
      const increasedCollects = link.current_collects - link.original_collects;
      
      // 状态转换为中文
      let statusText = '';
      switch (link.status) {
        case 'active': statusText = '活跃'; break;
        case 'paused': statusText = '暂停'; break;
        case 'completed': statusText = '完成'; break;
        case 'error': statusText = '错误'; break;
        default: statusText = link.status;
      }
      
      // 优先级转换为中文
      let priorityText = '';
      switch (link.priority) {
        case 'high': priorityText = '高'; break;
        case 'medium': priorityText = '中'; break;
        case 'low': priorityText = '低'; break;
        default: priorityText = link.priority;
      }
      
      // 转义CSV字段中的双引号和逗号
      const escapeCSV = (field) => {
        if (field === null || field === undefined) return '';
        const str = String(field);
        if (str.includes(',') || str.includes('"') || str.includes('\n')) {
          return `"${str.replace(/"/g, '""')}"`;
        }
        return str;
      };
      
      csv += [
        link.id,
        escapeCSV(link.url),
        link.original_likes,
        link.current_likes,
        increasedLikes,
        link.original_collects,
        link.current_collects,
        increasedCollects,
        link.target_likes,
        link.target_collects,
        link.fail_count,
        priorityText,
        statusText,
        escapeCSV(link.task_name || `任务 #${link.task_id}`),
        moment(link.created_at).format('YYYY-MM-DD HH:mm:ss')
      ].join(',') + '\n';
    }
    
    res.send(csv);
  } catch (error) {
    console.error('导出链接列表错误:', error);
    res.status(500).send('导出失败: ' + error.message);
  }
});

// 导出任务链接列表为CSV
router.get('/tasks/:id/export', requireLogin, async (req, res) => {
  try {
    const task = await Task.getById(req.params.id);
    
    if (!task || task.user_id !== req.user.id) {
      return res.status(403).send('无权访问此任务');
    }
    
    const filters = {
      status: req.query.status,
      orderBy: req.query.orderBy || 'created_at',
      orderDir: req.query.orderDir || 'DESC',
      search: req.query.search || ''
    };
    
    // 获取所有链接（不分页）
    const links = await Link.getByTaskId(task.id, filters);
    
    // 设置响应头，指定文件类型和下载文件名
    res.setHeader('Content-Type', 'text/csv; charset=utf-8');
    res.setHeader('Content-Disposition', `attachment; filename=task-${task.id}-links-export-${moment().format('YYYY-MM-DD-HH-mm-ss')}.csv`);
    
    // CSV表头
    let csv = 'ID,链接地址,初始点赞,当前点赞,增加点赞,初始收藏,当前收藏,增加收藏,点赞目标,收藏目标,失败次数,优先级,状态,创建时间\n';
    
    // 添加数据行
    for (const link of links) {
      const increasedLikes = link.current_likes - link.original_likes;
      const increasedCollects = link.current_collects - link.original_collects;
      
      // 状态转换为中文
      let statusText = '';
      switch (link.status) {
        case 'active': statusText = '活跃'; break;
        case 'paused': statusText = '暂停'; break;
        case 'completed': statusText = '完成'; break;
        case 'error': statusText = '错误'; break;
        default: statusText = link.status;
      }
      
      // 优先级转换为中文
      let priorityText = '';
      switch (link.priority) {
        case 'high': priorityText = '高'; break;
        case 'medium': priorityText = '中'; break;
        case 'low': priorityText = '低'; break;
        default: priorityText = link.priority;
      }
      
      // 转义CSV字段中的双引号和逗号
      const escapeCSV = (field) => {
        if (field === null || field === undefined) return '';
        const str = String(field);
        if (str.includes(',') || str.includes('"') || str.includes('\n')) {
          return `"${str.replace(/"/g, '""')}"`;
        }
        return str;
      };
      
      csv += [
        link.id,
        escapeCSV(link.url),
        link.original_likes,
        link.current_likes,
        increasedLikes,
        link.original_collects,
        link.current_collects,
        increasedCollects,
        link.target_likes,
        link.target_collects,
        link.fail_count,
        priorityText,
        statusText,
        moment(link.created_at).format('YYYY-MM-DD HH:mm:ss')
      ].join(',') + '\n';
    }
    
    res.send(csv);
  } catch (error) {
    console.error('导出任务链接列表错误:', error);
    res.status(500).send('导出失败: ' + error.message);
  }
});

module.exports = router; 