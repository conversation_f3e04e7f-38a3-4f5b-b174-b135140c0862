<!-- 确保Bootstrap库可用 -->
<script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.2.3/js/bootstrap.bundle.min.js"></script>

<!-- 添加jQuery库 -->
<script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>

<!-- 添加全局删除函数 -->
<script>
// 全局函数，用于确认删除并处理视觉效果
function confirmDelete(linkId) {
  // 确认删除
  if(!confirm('确定要删除此链接吗？此操作无法撤销！')) {
    return false; // 取消提交
  }
  
  // 获取行元素
  const row = document.getElementById('link-row-' + linkId);
  
  // 添加淡出效果
  if(row) {
    row.style.transition = 'opacity 0.5s';
    row.style.opacity = '0';
    
    // 500毫秒后移除行
    setTimeout(function() {
      row.remove();
      
      // 检查是否还有数据行
      const rows = document.querySelectorAll('table.table-links tbody tr');
      if(rows.length === 0) {
        const tableContainer = document.querySelector('.table-responsive');
        tableContainer.innerHTML = '<p class="text-center">暂无链接数据</p>';
      }
      
      // 显示成功消息
      const alertDiv = document.createElement('div');
      alertDiv.className = 'alert alert-success alert-dismissible fade show';
      alertDiv.role = 'alert';
      alertDiv.innerHTML = `
        链接已成功删除
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
      `;
      
      // 添加到页面顶部
      const container = document.querySelector('.card-body');
      container.insertBefore(alertDiv, container.firstChild);
      
      // 3秒后自动消失
      setTimeout(() => {
        alertDiv.classList.remove('show');
        setTimeout(() => {
          alertDiv.remove();
        }, 150);
      }, 3000);
    }, 500);
  }
  
  return true;
}
</script>

<!-- 添加一个隐藏的iframe用于表单提交 -->
<iframe name="hidden-iframe" style="display:none;"></iframe>

<div class="d-flex justify-content-between align-items-center mb-4">
  <h1>链接管理</h1>
  <div>
    <a href="/links/export<%= originalUrl ? originalUrl.replace('/links', '') : '' %>" class="btn btn-success">
      <i class="bi bi-file-earmark-excel"></i> 导出为CSV
    </a>
  </div>
</div>

<!-- 筛选器 -->
<div class="card mb-4 filter-card">
  <div class="card-body py-3">
    <form method="GET" action="/links">
      <!-- 所有内容在一行显示，平均分布 -->
      <div class="d-flex align-items-center justify-content-between flex-wrap w-100">
        <!-- 左侧筛选组 -->
        <div class="d-flex align-items-center" style="gap: 1.2rem;">
          <h6 class="mb-0" style="color: #495057; font-weight: 600;">筛选</h6>
          <select id="status" name="status" class="form-select form-select-sm" style="width: 100px;">
            <option value="">状态</option>
            <option value="active" <%= filters.status === 'active' ? 'selected' : '' %>>活跃</option>
            <option value="paused" <%= filters.status === 'paused' ? 'selected' : '' %>>暂停</option>
            <option value="completed" <%= filters.status === 'completed' ? 'selected' : '' %>>完成</option>
            <option value="error" <%= filters.status === 'error' ? 'selected' : '' %>>错误</option>
          </select>
          <select id="priority" name="priority" class="form-select form-select-sm" style="width: 100px;">
            <option value="">优先级</option>
            <option value="high" <%= filters.priority === 'high' ? 'selected' : '' %>>高</option>
            <option value="medium" <%= filters.priority === 'medium' ? 'selected' : '' %>>中</option>
            <option value="low" <%= filters.priority === 'low' ? 'selected' : '' %>>低</option>
          </select>
          <select id="task_id" name="task_id" class="form-select form-select-sm" style="width: 140px;">
            <option value="">任务</option>
            <% if (tasks && tasks.length > 0) { %>
              <% tasks.forEach(task => { %>
                <option value="<%= task.id %>" <%= filters.task_id == task.id ? 'selected' : '' %>><%= task.name %></option>
              <% }) %>
            <% } %>
          </select>
          <select id="failCount" name="failCount" class="form-select form-select-sm" style="width: 110px;">
            <option value="">失败次数</option>
            <option value="1" <%= filters.failCount === 1 ? 'selected' : '' %>>≥ 1</option>
            <option value="3" <%= filters.failCount === 3 ? 'selected' : '' %>>≥ 3</option>
            <option value="5" <%= filters.failCount === 5 ? 'selected' : '' %>>≥ 5</option>
          </select>
          <select id="orderBy" name="orderBy" class="form-select form-select-sm" style="width: 140px;">
            <option value="created_at" <%= filters.orderBy === 'created_at' ? 'selected' : '' %>>创建时间</option>
            <option value="last_operation_time" <%= filters.orderBy === 'last_operation_time' ? 'selected' : '' %>>最后操作时间</option>
            <option value="like_count" <%= filters.orderBy === 'like_count' ? 'selected' : '' %>>点赞次数</option>
            <option value="fail_count" <%= filters.orderBy === 'fail_count' ? 'selected' : '' %>>失败次数</option>
          </select>
        </div>

        <!-- 右侧搜索组 -->
        <div class="d-flex align-items-center" style="gap: 1rem;">
          <h6 class="mb-0" style="color: #495057; font-weight: 600;">搜索</h6>
          <input type="text" class="form-control form-control-sm" id="search" name="search" placeholder="输入链接关键字" value="<%= filters.search || '' %>" style="width: 250px;">
          <button type="submit" class="btn btn-primary btn-sm px-3">应用筛选</button>
          <a href="/links" class="btn btn-outline-secondary btn-sm px-3">重置</a>
        </div>
      </div>
    </form>
  </div>
</div>

<!-- 添加CSS动画和筛选器样式 -->
<style>
.fade-out {
  animation: fadeOut 0.5s;
  animation-fill-mode: forwards;
}

/* 筛选器优化样式 */
.filter-card .card-body {
  background-color: #f8f9fa;
}

.filter-card .form-select-sm,
.filter-card .form-control-sm {
  font-size: 0.875rem;
}

.filter-card h6 {
  color: #495057;
  font-weight: 600;
  white-space: nowrap;
}

/* 响应式优化 */
@media (max-width: 1400px) {
  .filter-card .d-flex.justify-content-between {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 1rem;
  }

  .filter-card .d-flex.align-items-center {
    gap: 0.75rem !important;
  }

  .filter-card input[type="text"] {
    width: 200px !important;
  }
}

@media (max-width: 1200px) {
  .filter-card .d-flex.align-items-center {
    gap: 0.5rem !important;
  }

  .filter-card input[type="text"] {
    width: 180px !important;
  }

  .filter-card select {
    width: 80px !important;
  }

  .filter-card select[id="task_id"],
  .filter-card select[id="orderBy"] {
    width: 100px !important;
  }
}

@media (max-width: 768px) {
  .filter-card .btn-sm {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
  }

  .filter-card input[type="text"] {
    width: 150px !important;
  }

  .filter-card select {
    width: 70px !important;
  }

  .filter-card select[id="task_id"],
  .filter-card select[id="orderBy"] {
    width: 90px !important;
  }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}
</style>

<!-- 批量操作栏 -->
<div class="card mb-3" id="batchOperations" style="display: none;">
  <div class="card-body py-2">
    <div class="d-flex align-items-center justify-content-between">
      <div class="d-flex align-items-center">
        <span class="me-3">已选择 <span id="selectedCount">0</span> 个链接</span>
        <div class="btn-group" role="group">
          <button type="button" class="btn btn-sm btn-outline-primary" onclick="batchUpdateStatus('active')">
            <i class="bi bi-play-fill"></i> 批量激活
          </button>
          <button type="button" class="btn btn-sm btn-outline-warning" onclick="batchUpdateStatus('paused')">
            <i class="bi bi-pause-fill"></i> 批量暂停
          </button>
          <button type="button" class="btn btn-sm btn-outline-success" onclick="batchUpdateStatus('completed')">
            <i class="bi bi-check-circle-fill"></i> 批量完成
          </button>
          <button type="button" class="btn btn-sm btn-outline-danger" onclick="batchDelete()">
            <i class="bi bi-trash-fill"></i> 批量删除
          </button>
        </div>
      </div>
      <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearSelection()">
        <i class="bi bi-x"></i> 取消选择
      </button>
    </div>
  </div>
</div>

<!-- 链接列表 -->
<div class="card">
  <div class="card-body">
    <% if (links && links.length > 0) { %>
      <div class="table-responsive">
        <table class="table table-striped table-links">
          <thead>
            <tr>
              <th style="width: 40px;">
                <input type="checkbox" id="selectAll" class="form-check-input" onclick="toggleAllCheckboxes(this)">
              </th>
              <th>ID</th>
              <th>链接</th>
              <th>初始点赞</th>
              <th>当前点赞</th>
              <th>初始收藏</th>
              <th>当前收藏</th>
              <th>点赞/目标</th>
              <th>收藏/目标</th>
              <th>失败次数</th>
              <th>优先级</th>
              <th>状态</th>
              <th>任务</th>
              <th>创建时间</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <% links.forEach(link => { %>
              <tr id="link-row-<%= link.id %>">
                <td>
                  <input type="checkbox" class="form-check-input link-checkbox" value="<%= link.id %>" data-link-id="<%= link.id %>">
                </td>
                <td><span class="badge bg-dark text-white"><%= link.id %></span></td>
                <td>
                  <a href="<%= link.url %>" target="_blank" class="text-truncate d-inline-block" style="max-width: 200px;">
                    <%= link.url %>
                  </a>
                </td>
                <td><%= link.original_likes %></td>
                <td>
                  <%= link.current_likes %>
                  <% if (link.current_likes > link.original_likes) { %>
                    <span class="text-success">(+<%= link.current_likes - link.original_likes %>)</span>
                  <% } %>
                </td>
                <td><%= link.original_collects %></td>
                <td>
                  <%= link.current_collects %>
                  <% if (link.current_collects > link.original_collects) { %>
                    <span class="text-success">(+<%= link.current_collects - link.original_collects %>)</span>
                  <% } %>
                </td>
                <td>
                  <%= link.like_count %>/<%= link.target_likes %>
                  <% if (link.target_likes > 0) { %>
                    (<%= Math.round((link.like_count / link.target_likes) * 100) %>%)
                  <% } %>
                </td>
                <td>
                  <%= link.collect_count %>/<%= link.target_collects %>
                  <% if (link.target_collects > 0) { %>
                    (<%= Math.round((link.collect_count / link.target_collects) * 100) %>%)
                  <% } %>
                </td>
                <td><%= link.fail_count %></td>
                <td>
                  <span class="badge priority-<%= link.priority %>">
                    <% if (link.priority === 'high') { %>高
                    <% } else if (link.priority === 'medium') { %>中
                    <% } else { %>低<% } %>
                  </span>
                </td>
                <td>
                  <span class="badge status-<%= link.status %>">
                    <% if (link.status === 'active') { %>活跃
                    <% } else if (link.status === 'paused') { %>暂停
                    <% } else if (link.status === 'completed') { %>完成
                    <% } else { %>错误<% } %>
                  </span>
                </td>
                <td>
                  <% if (link.task_id) { %>
                    <a href="/tasks/<%= link.task_id %>"><%= link.task_name || '任务 #' + link.task_id %></a>
                  <% } else { %>
                    <span class="text-danger">未分配</span>
                  <% } %>
                </td>
                <td><%= moment(link.created_at).format('YYYY-MM-DD') %></td>
                <td>
                  <div class="btn-group">
                    <a href="/links/edit/<%= link.id %>" class="btn btn-sm btn-outline-primary">编辑</a>
                    <!-- 使用普通表单提交 -->
                    <form method="POST" action="/links/delete/<%= link.id %>" style="display:inline;">
                      <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('确定要删除此链接吗？此操作无法撤销！')">删除</button>
                    </form>
                  </div>
                </td>
              </tr>
            <% }) %>
          </tbody>
        </table>
      </div>
      
      <!-- 分页控件 -->
      <% if (typeof totalPages !== 'undefined' && totalPages > 1) { %>
        <div class="d-flex justify-content-between align-items-center mt-4">
          <div class="d-flex align-items-center">
            <form id="perPageForm" method="GET" action="" class="d-flex align-items-center">
              <span class="me-2">每页显示:</span>
              <select class="form-select form-select-sm ms-2" style="width: auto;" id="perPageSelect" name="perPage" onchange="this.form.submit()">
                <option value="20" <%= (perPage || 20) == 20 ? 'selected' : '' %>>20</option>
                <option value="30" <%= (perPage || 20) == 30 ? 'selected' : '' %>>30</option>
                <option value="50" <%= (perPage || 20) == 50 ? 'selected' : '' %>>50</option>
                <option value="80" <%= (perPage || 20) == 80 ? 'selected' : '' %>>80</option>
                <option value="100" <%= (perPage || 20) == 100 ? 'selected' : '' %>>100</option>
              </select>
              <!-- 保留当前筛选参数 -->
              <% if (filters.status) { %><input type="hidden" name="status" value="<%= filters.status %>"><% } %>
              <% if (filters.priority) { %><input type="hidden" name="priority" value="<%= filters.priority %>"><% } %>
              <% if (filters.task_id) { %><input type="hidden" name="task_id" value="<%= filters.task_id %>"><% } %>
              <% if (filters.failCount) { %><input type="hidden" name="failCount" value="<%= filters.failCount %>"><% } %>
              <% if (filters.orderBy) { %><input type="hidden" name="orderBy" value="<%= filters.orderBy %>"><% } %>
              <% if (filters.orderDir) { %><input type="hidden" name="orderDir" value="<%= filters.orderDir %>"><% } %>
            </form>
          </div>
          <nav aria-label="链接列表分页">
            <ul class="pagination justify-content-center mb-0">
              <li class="page-item <%= currentPage <= 1 ? 'disabled' : '' %>">
                <a class="page-link" href="<%= getPageUrl(1) %>" aria-label="首页">
                  <span aria-hidden="true">&laquo;&laquo;</span>
                </a>
              </li>
              <li class="page-item <%= currentPage <= 1 ? 'disabled' : '' %>">
                <a class="page-link" href="<%= getPageUrl(currentPage - 1) %>" aria-label="上一页">
                  <span aria-hidden="true">&laquo;</span>
                </a>
              </li>
              
              <% 
                let startPage = Math.max(1, currentPage - 2);
                let endPage = Math.min(totalPages, startPage + 4);
                if (endPage - startPage < 4) {
                  startPage = Math.max(1, endPage - 4);
                }
              %>
              
              <% for (let i = startPage; i <= endPage; i++) { %>
                <li class="page-item <%= i === currentPage ? 'active' : '' %>">
                  <a class="page-link" href="<%= getPageUrl(i) %>"><%= i %></a>
                </li>
              <% } %>
              
              <li class="page-item <%= currentPage >= totalPages ? 'disabled' : '' %>">
                <a class="page-link" href="<%= getPageUrl(currentPage + 1) %>" aria-label="下一页">
                  <span aria-hidden="true">&raquo;</span>
                </a>
              </li>
              <li class="page-item <%= currentPage >= totalPages ? 'disabled' : '' %>">
                <a class="page-link" href="<%= getPageUrl(totalPages) %>" aria-label="末页">
                  <span aria-hidden="true">&raquo;&raquo;</span>
                </a>
              </li>
            </ul>
          </nav>
        </div>
      <% } else if (links.length > 0) { %>
        <div class="d-flex justify-content-start align-items-center mt-4">
          <form id="perPageForm" method="GET" action="" class="d-flex align-items-center">
            <span class="me-2">每页显示:</span>
            <select class="form-select form-select-sm ms-2" style="width: auto;" id="perPageSelect" name="perPage" onchange="this.form.submit()">
              <option value="20" <%= (perPage || 20) == 20 ? 'selected' : '' %>>20</option>
              <option value="30" <%= (perPage || 20) == 30 ? 'selected' : '' %>>30</option>
              <option value="50" <%= (perPage || 20) == 50 ? 'selected' : '' %>>50</option>
              <option value="80" <%= (perPage || 20) == 80 ? 'selected' : '' %>>80</option>
              <option value="100" <%= (perPage || 20) == 100 ? 'selected' : '' %>>100</option>
            </select>
            <!-- 保留当前筛选参数 -->
            <% if (filters.status) { %><input type="hidden" name="status" value="<%= filters.status %>"><% } %>
            <% if (filters.priority) { %><input type="hidden" name="priority" value="<%= filters.priority %>"><% } %>
            <% if (filters.task_id) { %><input type="hidden" name="task_id" value="<%= filters.task_id %>"><% } %>
            <% if (filters.failCount) { %><input type="hidden" name="failCount" value="<%= filters.failCount %>"><% } %>
            <% if (filters.orderBy) { %><input type="hidden" name="orderBy" value="<%= filters.orderBy %>"><% } %>
            <% if (filters.orderDir) { %><input type="hidden" name="orderDir" value="<%= filters.orderDir %>"><% } %>
          </form>
        </div>
      <% } %>
    <% } else { %>
      <p class="text-center">暂无链接数据</p>
    <% } %>
  </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="deleteConfirmModalLabel">确认删除</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        确定要删除这个链接吗？此操作无法撤销。
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
        <button type="button" class="btn btn-danger" id="confirmDeleteBtn">删除</button>
      </div>
    </div>
  </div>
</div>

<!-- 使用jQuery处理删除功能和勾选框功能 -->
<script type="text/javascript">
// 简单的全选函数 - 放在全局作用域
function toggleAllCheckboxes(selectAllCheckbox) {
  console.log('toggleAllCheckboxes被调用，状态:', selectAllCheckbox.checked);
  var checkboxes = document.querySelectorAll('.link-checkbox');
  console.log('找到的勾选框数量:', checkboxes.length);

  for (var i = 0; i < checkboxes.length; i++) {
    checkboxes[i].checked = selectAllCheckbox.checked;
  }

  updateBatchOperations();
}

// 更新批量操作栏显示
function updateBatchOperations() {
  var checkedCount = document.querySelectorAll('.link-checkbox:checked').length;
  console.log('已选择链接数:', checkedCount);

  var selectedCountElement = document.getElementById('selectedCount');
  var batchOperationsElement = document.getElementById('batchOperations');

  if (selectedCountElement) {
    selectedCountElement.textContent = checkedCount;
  }

  if (batchOperationsElement) {
    if (checkedCount > 0) {
      batchOperationsElement.style.display = 'block';
    } else {
      batchOperationsElement.style.display = 'none';
    }
  }
}

// 等待文档加载完成
$(document).ready(function() {
  console.log('jQuery已加载，版本:', $.fn.jquery);
  console.log('页面加载完成，勾选框数量:', $('.link-checkbox').length);

  // 直接绑定全选事件
  $('#selectAll').click(function() {
    console.log('全选按钮被点击，当前状态:', this.checked);
    $('.link-checkbox').prop('checked', this.checked);
    updateBatchOperations();
  });

  // 直接绑定单个勾选框事件
  $('.link-checkbox').click(function() {
    console.log('单个勾选框被点击');
    updateBatchOperations();

    // 检查是否所有勾选框都被选中
    var total = $('.link-checkbox').length;
    var checked = $('.link-checkbox:checked').length;
    console.log('勾选状态:', checked, '/', total);
    $('#selectAll').prop('checked', total === checked);
  });

  // 初始化批量操作栏状态
  updateBatchOperations();
});
  
  // 为所有删除按钮绑定点击事件
  $('.delete-link').on('click', function() {
    console.log('删除按钮被点击');
    
    // 获取链接ID
    var linkId = $(this).data('id');
    console.log('链接ID:', linkId);
    
    // 确认删除
    if(!confirm('确定要删除此链接吗？此操作无法撤销！')) {
      return false;
    }
    
    // 获取行元素
    var row = $('#link-row-' + linkId);
    console.log('行元素:', row.length > 0 ? '找到' : '未找到');
    
    // 添加淡出效果
    row.css('transition', 'opacity 0.5s').css('opacity', '0');
    
    // 创建并提交表单
    var form = $('<form>')
      .attr('method', 'POST')
      .attr('action', '/links/delete/' + linkId)
      .attr('target', 'hidden-iframe')
      .css('display', 'none');
    
    $('body').append(form);
    console.log('表单已创建');
    
    form.submit();
    console.log('表单已提交');
    
    // 500毫秒后移除行
    setTimeout(function() {
      row.remove();
      console.log('行已移除');
      
      // 检查是否还有数据行
      if($('table.table-links tbody tr').length === 0) {
        $('.table-responsive').html('<p class="text-center">暂无链接数据</p>');
      }
      
      // 显示成功消息
      var alertHtml = '<div class="alert alert-success alert-dismissible fade show" role="alert">' +
                      '链接已成功删除' +
                      '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
                      '</div>';
      
      $('.card-body').prepend(alertHtml);
      
      // 3秒后自动消失
      setTimeout(function() {
        $('.alert').alert('close');
      }, 3000);
    }, 500);
  });
});

// 批量操作函数
function clearSelection() {
  $('.link-checkbox').prop('checked', false);
  $('#selectAll').prop('checked', false);
  $('#batchOperations').hide();
}

function getSelectedLinkIds() {
  const selectedIds = [];
  $('.link-checkbox:checked').each(function() {
    selectedIds.push($(this).val());
  });
  return selectedIds;
}

function batchUpdateStatus(status) {
  const selectedIds = getSelectedLinkIds();
  if (selectedIds.length === 0) {
    alert('请先选择要操作的链接');
    return;
  }

  const statusText = {
    'active': '激活',
    'paused': '暂停',
    'completed': '完成'
  };

  if (!confirm(`确定要将选中的 ${selectedIds.length} 个链接状态设置为"${statusText[status]}"吗？`)) {
    return;
  }

  // 创建表单并提交
  const form = $('<form>')
    .attr('method', 'POST')
    .attr('action', '/links/batch-update-status')
    .css('display', 'none');

  // 添加链接ID
  selectedIds.forEach(id => {
    form.append($('<input>').attr('type', 'hidden').attr('name', 'linkIds[]').val(id));
  });

  // 添加状态
  form.append($('<input>').attr('type', 'hidden').attr('name', 'status').val(status));

  $('body').append(form);
  form.submit();
}

function batchDelete() {
  const selectedIds = getSelectedLinkIds();
  if (selectedIds.length === 0) {
    alert('请先选择要删除的链接');
    return;
  }

  if (!confirm(`确定要删除选中的 ${selectedIds.length} 个链接吗？此操作无法撤销！`)) {
    return;
  }

  // 创建表单并提交
  const form = $('<form>')
    .attr('method', 'POST')
    .attr('action', '/links/batch-delete')
    .css('display', 'none');

  // 添加链接ID
  selectedIds.forEach(id => {
    form.append($('<input>').attr('type', 'hidden').attr('name', 'linkIds[]').val(id));
  });

  $('body').append(form);
  form.submit();
}
</script>