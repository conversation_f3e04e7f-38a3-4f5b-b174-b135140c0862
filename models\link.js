const db = require('../utils/db');
const config = require('../config/config');
const Setting = require('./setting');

class Link {
  // 根据ID获取链接
  static async getById(id) {
    const sql = 'SELECT * FROM links WHERE id = ?';
    const [results] = await db.query(sql, [id]);
    return results.length ? results[0] : null;
  }

  // 获取用户的所有链接
  static async getAllByUserId(userId, filters = {}, limit = null, offset = null) {
    let sql = 'SELECT * FROM links WHERE user_id = ?';
    const params = [userId];

    // 添加筛选条件
    if (filters.status) {
      sql += ' AND status = ?';
      params.push(filters.status);
    }

    if (filters.priority) {
      sql += ' AND priority = ?';
      params.push(filters.priority);
    }
    
    if (filters.batch) {
      sql += ' AND batch = ?';
      params.push(filters.batch);
    }
    
    if (filters.task_id) {
      sql += ' AND task_id = ?';
      params.push(filters.task_id);
    }

    if (filters.failCount) {
      sql += ' AND fail_count >= ?';
      params.push(filters.failCount);
    }

    if (filters.dateFrom) {
      sql += ' AND created_at >= ?';
      params.push(filters.dateFrom);
    }

    if (filters.dateTo) {
      sql += ' AND created_at <= ?';
      params.push(filters.dateTo);
    }
    
    // 添加搜索条件
    if (filters.search) {
      sql += ' AND url LIKE ?';
      params.push(`%${filters.search}%`);
    }

    // 排序
    sql += ' ORDER BY ';
    if (filters.orderBy) {
      sql += `${filters.orderBy} ${filters.orderDir || 'DESC'}`;
    } else {
      sql += 'created_at DESC';
    }
    
    // 添加分页
    if (limit !== null) {
      sql += ' LIMIT ?';
      params.push(limit);
      
      if (offset !== null) {
        sql += ' OFFSET ?';
        params.push(offset);
      }
    }

    const [results] = await db.query(sql, params);
    return results;
  }

  // 计算用户链接总数（用于分页）
  static async countByUserId(userId, filters = {}) {
    let sql = 'SELECT COUNT(*) as count FROM links WHERE user_id = ?';
    const params = [userId];

    // 添加筛选条件
    if (filters.status) {
      sql += ' AND status = ?';
      params.push(filters.status);
    }

    if (filters.priority) {
      sql += ' AND priority = ?';
      params.push(filters.priority);
    }
    
    if (filters.batch) {
      sql += ' AND batch = ?';
      params.push(filters.batch);
    }
    
    if (filters.task_id) {
      sql += ' AND task_id = ?';
      params.push(filters.task_id);
    }

    if (filters.failCount) {
      sql += ' AND fail_count >= ?';
      params.push(filters.failCount);
    }

    if (filters.dateFrom) {
      sql += ' AND created_at >= ?';
      params.push(filters.dateFrom);
    }

    if (filters.dateTo) {
      sql += ' AND created_at <= ?';
      params.push(filters.dateTo);
    }
    
    // 添加搜索条件
    if (filters.search) {
      sql += ' AND url LIKE ?';
      params.push(`%${filters.search}%`);
    }

    const [results] = await db.query(sql, params);
    return results[0].count;
  }

  // 创建链接
  static async create(linkData) {
    const { 
      user_id, url, original_likes = 0, original_collects = 0, original_comments = 0,
      target_likes = 0, target_collects = 0, target_comments = 0, 
      like_target = 0, collect_target = 0, comment_target = 0,
      priority = 'medium', task_id, status = 'active'
    } = linkData;
    
    // 确保所有值都有默认值，不会是undefined
    const safeOriginalLikes = original_likes || 0;
    const safeOriginalCollects = original_collects || 0;
    const safeOriginalComments = original_comments || 0;
    const safeTargetLikes = target_likes || 0;
    const safeTargetCollects = target_collects || 0;
    const safeTargetComments = target_comments || 0;
    const safeLikeTarget = like_target || safeTargetLikes;
    const safeCollectTarget = collect_target || safeTargetCollects;
    const safeCommentTarget = comment_target || safeTargetComments;
    const safePriority = priority || 'medium';
    const safeStatus = status || 'active';
    
    // 确保task_id不是undefined
    if (task_id === undefined) {
      throw new Error('任务ID不能为空');
    }
    
    const sql = `
      INSERT INTO links 
      (user_id, url, original_likes, current_likes, original_collects, 
      current_collects, original_comments, current_comments, target_likes, 
      target_collects, target_comments, like_target, collect_target, comment_target, priority, status, task_id) 
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const [result] = await db.query(sql, [
      user_id, url, safeOriginalLikes, safeOriginalLikes, safeOriginalCollects, 
      safeOriginalCollects, safeOriginalComments, safeOriginalComments, safeTargetLikes, 
      safeTargetCollects, safeTargetComments, safeLikeTarget, safeCollectTarget, safeCommentTarget, 
      safePriority, safeStatus, task_id
    ]);
    
    return result.insertId;
  }

  // 更新链接
  static async update(id, linkData) {
    const fields = [];
    const values = [];

    // 动态构建更新字段
    for (const [key, value] of Object.entries(linkData)) {
      if (value !== undefined) {
        fields.push(`${key} = ?`);
        values.push(value);
      }
    }

    if (fields.length === 0) return null;

    values.push(id);
    const sql = `UPDATE links SET ${fields.join(', ')}, updated_at = NOW() WHERE id = ?`;
    
    const [result] = await db.query(sql, values);
    return result;
  }

  // 删除链接
  static async delete(id) {
    const sql = 'DELETE FROM links WHERE id = ?';
    const [result] = await db.query(sql, [id]);
    return result;
  }

  // 更新链接状态
  static async updateStatus(id, status) {
    const sql = 'UPDATE links SET status = ?, updated_at = NOW() WHERE id = ?';
    const [result] = await db.query(sql, [status, id]);
    return result;
  }

  // 增加失败次数
  static async incrementFailCount(id) {
    const sql = 'UPDATE links SET fail_count = fail_count + 1, updated_at = NOW() WHERE id = ?';
    const [result] = await db.query(sql, [id]);
    
    // 检查是否超过最大失败次数
    const link = await this.getById(id);
    if (link && link.fail_count >= config.business.max_fail_count) {
      await this.updateStatus(id, 'error');
    }
    
    return result;
  }

  // 重置失败次数
  static async resetFailCount(id) {
    const sql = 'UPDATE links SET fail_count = 0, updated_at = NOW() WHERE id = ?';
    const [result] = await db.query(sql, [id]);
    return result;
  }

  // 更新点赞/收藏/评论数据
  static async updateLikeCollectData(id, data) {
    const { likes_count, collects_count, comments_count, operation_type } = data;
    
    let sql = 'UPDATE links SET current_likes = ?, updated_at = NOW(), last_operation_time = NOW()';
    const params = [likes_count];
    
    if (operation_type === 'like') {
      sql += ', like_count = like_count + 1';
    }
    
    if (collects_count !== undefined) {
      sql += ', current_collects = ?';
      params.push(collects_count);
      
      if (operation_type === 'collect') {
        sql += ', collect_count = collect_count + 1';
      }
    }
    
    if (comments_count !== undefined) {
      sql += ', current_comments = ?';
      params.push(comments_count);
      
      if (operation_type === 'comment') {
        sql += ', comment_count = comment_count + 1';
      }
    }
    
    sql += ' WHERE id = ?';
    params.push(id);
    
    const [result] = await db.query(sql, params);
    return result;
  }

  // 检查链接是否已存在
  static async checkExists(userId, url) {
    const sql = 'SELECT * FROM links WHERE user_id = ? AND url = ?';
    const [results] = await db.query(sql, [userId, url]);
    return results.length > 0 ? results[0] : null;
  }

  // 检查链接是否在同一任务中已存在
  static async checkExistsInTask(userId, url, taskId) {
    const sql = 'SELECT * FROM links WHERE user_id = ? AND url = ? AND task_id = ?';
    const [results] = await db.query(sql, [userId, url, taskId]);
    return results.length > 0 ? results[0] : null;
  }

  // 获取下一个待处理的链接
  static async getNextLink(userId, deviceId) {
    // 获取设备正在处理的链接
    const processingLinkSql = `
      SELECT * FROM links 
      WHERE user_id = ? AND status = 'processing' AND device_id = ?
      LIMIT 1
    `;
    
    const [processingLinks] = await db.query(processingLinkSql, [userId, deviceId]);
    if (processingLinks.length > 0) {
      return processingLinks[0];
    }
    
    // 获取下一个待处理的链接，先按任务优先级、再按链接优先级和创建时间排序
    const nextLinkSql = `
      SELECT l.* 
      FROM links l
      LEFT JOIN tasks t ON l.task_id = t.id
      WHERE l.user_id = ? AND l.status = 'active' AND l.device_id IS NULL
      ORDER BY 
        CASE t.priority
          WHEN 'high' THEN 1
          WHEN 'medium' THEN 2
          WHEN 'low' THEN 3
          ELSE 4
        END,
        CASE l.priority
          WHEN 'high' THEN 1
          WHEN 'medium' THEN 2
          WHEN 'low' THEN 3
          ELSE 4
        END,
        l.created_at ASC
      LIMIT 1
    `;
    
    const [nextLinks] = await db.query(nextLinkSql, [userId]);
    if (nextLinks.length > 0) {
      const link = nextLinks[0];
      
      // 将链接标记为正在处理，并分配给设备
      await this.update(link.id, {
        status: 'processing',
        device_id: deviceId
      });
      
      return link;
    }
    
    return null;
  }

  // 获取所有批次名称
  static async getAllBatches(userId) {
    const sql = 'SELECT DISTINCT batch FROM links WHERE user_id = ? AND batch IS NOT NULL';
    const [results] = await db.query(sql, [userId]);
    return results.map(row => row.batch);
  }

  // 激活批次中的所有链接
  static async activateBatch(userId, batchName) {
    const sql = 'UPDATE links SET status = "active", updated_at = NOW() WHERE user_id = ? AND batch = ?';
    const [result] = await db.query(sql, [userId, batchName]);
    return result;
  }

  // 暂停批次中的所有链接
  static async pauseBatch(userId, batchName) {
    const sql = 'UPDATE links SET status = "paused", updated_at = NOW() WHERE user_id = ? AND batch = ?';
    const [result] = await db.query(sql, [userId, batchName]);
    return result;
  }

  // 重置批次中所有链接的失败次数
  static async resetBatchFailCount(userId, batchName) {
    const sql = 'UPDATE links SET fail_count = 0, updated_at = NOW() WHERE user_id = ? AND batch = ?';
    const [result] = await db.query(sql, [userId, batchName]);
    return result;
  }

  // 获取任务中的所有链接
  static async getByTaskId(taskId, filters = {}, limit = null, offset = null) {
    let sql = 'SELECT * FROM links WHERE task_id = ?';
    const params = [taskId];

    // 添加筛选条件
    if (filters.status) {
      sql += ' AND status = ?';
      params.push(filters.status);
    }
    
    // 添加搜索条件
    if (filters.search) {
      sql += ' AND url LIKE ?';
      params.push(`%${filters.search}%`);
    }

    // 排序
    sql += ' ORDER BY ';
    if (filters.orderBy) {
      sql += `${filters.orderBy} ${filters.orderDir || 'DESC'}`;
    } else {
      sql += 'created_at DESC';
    }
    
    // 添加分页
    if (limit !== null) {
      sql += ' LIMIT ?';
      params.push(limit);
      
      if (offset !== null) {
        sql += ' OFFSET ?';
        params.push(offset);
      }
    }

    const [results] = await db.query(sql, params);
    return results;
  }

  // 计算任务链接总数（用于分页）
  static async countByTaskId(taskId, filters = {}) {
    let sql = 'SELECT COUNT(*) as count FROM links WHERE task_id = ?';
    const params = [taskId];

    // 添加筛选条件
    if (filters.status) {
      sql += ' AND status = ?';
      params.push(filters.status);
    }
    
    // 添加搜索条件
    if (filters.search) {
      sql += ' AND url LIKE ?';
      params.push(`%${filters.search}%`);
    }

    const [results] = await db.query(sql, params);
    return results[0].count;
  }

  // 更新链接的任务ID
  static async updateTaskId(linkId, taskId) {
    const sql = 'UPDATE links SET task_id = ?, updated_at = NOW() WHERE id = ?';
    const [result] = await db.query(sql, [taskId, linkId]);
    return result;
  }

  // 批量更新链接的任务ID
  static async bulkUpdateTaskId(linkIds, taskId) {
    if (!linkIds.length) return { affectedRows: 0 };
    
    const placeholders = linkIds.map(() => '?').join(',');
    const sql = `UPDATE links SET task_id = ?, updated_at = NOW() WHERE id IN (${placeholders})`;
    
    const params = [taskId, ...linkIds];
    const [result] = await db.query(sql, params);
    return result;
  }

  // 激活任务中的所有链接
  static async activateTaskLinks(taskId) {
    const sql = 'UPDATE links SET status = "active", updated_at = NOW() WHERE task_id = ?';
    const [result] = await db.query(sql, [taskId]);
    return result;
  }

  // 暂停任务中的所有链接
  static async pauseTaskLinks(taskId) {
    const sql = 'UPDATE links SET status = "paused", updated_at = NOW() WHERE task_id = ?';
    const [result] = await db.query(sql, [taskId]);
    return result;
  }

  // 重置任务中所有链接的失败次数
  static async resetTaskFailCount(taskId) {
    const sql = 'UPDATE links SET fail_count = 0, updated_at = NOW() WHERE task_id = ?';
    const [result] = await db.query(sql, [taskId]);
    return result;
  }

  // 删除任务中的所有链接
  static async deleteAllByTaskId(taskId) {
    const sql = 'DELETE FROM links WHERE task_id = ?';
    const [result] = await db.query(sql, [taskId]);
    return result;
  }

  // 根据ID数组获取链接
  static async getByIds(linkIds) {
    if (!linkIds || !Array.isArray(linkIds) || linkIds.length === 0) {
      return [];
    }

    const placeholders = linkIds.map(() => '?').join(',');
    const sql = `SELECT * FROM links WHERE id IN (${placeholders})`;
    const [results] = await db.query(sql, linkIds);
    return results;
  }

  // 批量更新链接状态
  static async batchUpdateStatus(linkIds, status) {
    if (!linkIds || !Array.isArray(linkIds) || linkIds.length === 0) {
      return { affectedRows: 0 };
    }

    const placeholders = linkIds.map(() => '?').join(',');
    const sql = `UPDATE links SET status = ?, updated_at = NOW() WHERE id IN (${placeholders})`;
    const params = [status, ...linkIds];
    const [result] = await db.query(sql, params);
    return result;
  }

  // 批量删除链接
  static async batchDelete(linkIds) {
    if (!linkIds || !Array.isArray(linkIds) || linkIds.length === 0) {
      return { affectedRows: 0 };
    }

    const placeholders = linkIds.map(() => '?').join(',');
    const sql = `DELETE FROM links WHERE id IN (${placeholders})`;
    const [result] = await db.query(sql, linkIds);
    return result;
  }

  // 获取下一个待处理的链接（不分配给设备，仅更改状态）
  static async getNextPendingLink(userId) {
    // 获取下一个待处理的链接，考虑任务优先级和链接优先级
    const nextLinkSql = `
      SELECT l.* 
      FROM links l
      LEFT JOIN tasks t ON l.task_id = t.id
      WHERE l.user_id = ? AND l.status = 'active'
      ORDER BY 
        CASE t.priority
          WHEN 'high' THEN 1
          WHEN 'medium' THEN 2
          WHEN 'low' THEN 3
          ELSE 4
        END,
        CASE l.priority
          WHEN 'high' THEN 1
          WHEN 'medium' THEN 2
          WHEN 'low' THEN 3
          ELSE 4
        END,
        l.created_at ASC
      LIMIT 1
    `;
    
    const [nextLinks] = await db.query(nextLinkSql, [userId]);
    return nextLinks.length > 0 ? nextLinks[0] : null;
  }

  // 检查链接是否可操作（考虑并发操作限制）
  static async isLinkAvailableForOperation(userId, linkId) {
    try {
      // 获取链接并发操作限制设置
      const maxConcurrentOperations = await Setting.getValue(userId, 'link.max_concurrent_operations', '3');
      
      // 获取当前链接的处理设备数量
      const sql = `
        SELECT COUNT(*) as processing_count
        FROM links
        WHERE id = ? AND user_id = ? AND status = 'processing'
      `;
      
      const [results] = await db.query(sql, [linkId, userId]);
      const processingCount = results[0].processing_count;
      
      return processingCount < parseInt(maxConcurrentOperations);
    } catch (error) {
      console.error('检查链接可操作性错误:', error);
      return false;
    }
  }

  // 检查链接是否在冷却期
  static async isLinkInCooldown(userId, linkId) {
    try {
      // 获取冷却时间设置（秒）
      const cooldownSeconds = await Setting.getValue(userId, 'link.cooldown_seconds', '3600');
      
      // 查询最后操作时间
      const sql = `
        SELECT MAX(created_at) as last_operation
        FROM operation_logs
        WHERE link_id = ? AND user_id = ?
      `;
      
      const [results] = await db.query(sql, [linkId, userId]);
      if (!results[0].last_operation) {
        return false; // 没有操作记录，不在冷却期
      }
      
      const lastOperation = new Date(results[0].last_operation);
      const now = new Date();
      const diffSeconds = Math.floor((now - lastOperation) / 1000);
      
      return diffSeconds < parseInt(cooldownSeconds);
    } catch (error) {
      console.error('检查链接冷却期错误:', error);
      return true; // 错误时默认为在冷却期（安全）
    }
  }

  // 检查处理中的链接是否超时
  static async checkAndResetTimeoutLinks(userId) {
    try {
      // 引入链接设备分配模型
      const LinkDeviceAssignment = require('./link_device_assignment');
      
      // 获取处理超时时间设置（秒）
      const timeoutSeconds = await Setting.getValue(userId, 'link.processing_timeout_seconds', '80');
      
      // 使用链接设备分配表检查超时
      const resetCount = await LinkDeviceAssignment.checkAndResetTimeouts(userId, timeoutSeconds);
      
      console.log(`重置了 ${resetCount} 个超时链接分配`);
      return resetCount;
    } catch (error) {
      console.error('检查超时链接错误:', error);
      throw error;
    }
  }

  // 获取下一个待处理的链接（考虑并发和冷却限制）
  static async getNextAvailableLink(userId, deviceId) {
    try {
      // 引入链接设备分配模型
      const LinkDeviceAssignment = require('./link_device_assignment');
      const OperationLog = require('./operation_log');

      // 获取处理超时时间设置（秒）
      const timeoutSeconds = await Setting.getValue(userId, 'link.processing_timeout_seconds', '80');

      // 检查并标记超时的分配
      await LinkDeviceAssignment.checkAndResetTimeouts(userId, timeoutSeconds);

      // 检查设备是否已有活跃的链接分配
      const activeAssignment = await LinkDeviceAssignment.getDeviceActiveAssignment(deviceId);
      if (activeAssignment) {
        console.log(`设备(${deviceId})已有活跃分配的链接(${activeAssignment.link_id})`);
        // 获取完整的链接信息
        const link = await this.getById(activeAssignment.link_id);
        return link;
      }

      // 获取链接分配模式
      const allocationMode = await Setting.getValue(userId, 'link.allocation_mode', 'smart');

      if (allocationMode === 'round_robin') {
        // 使用循环分配模式
        return await this.getNextRoundRobinLink(userId, deviceId);
      } else {
        // 使用智能分配模式（原有逻辑）
        return await this.getNextSmartLink(userId, deviceId);
      }
      
    } catch (error) {
      console.error('获取下一个可用链接错误:', error);
      throw error;
    }
  }

  // 智能分配模式（原有逻辑）
  static async getNextSmartLink(userId, deviceId) {
    try {
      const LinkDeviceAssignment = require('./link_device_assignment');
      const OperationLog = require('./operation_log');

      // 获取最大重试次数设置
      const maxRetries = await Setting.getValue(userId, 'link.max_retries', '3');

      // 获取最大并发操作数设置
      const maxConcurrentOps = await Setting.getValue(userId, 'link.max_concurrent_operations', '3');

      // 获取冷却时间设置
      const cooldownSeconds = await Setting.getValue(userId, 'link.cooldown_seconds', '60');

      // 获取所有活跃链接并按优先级排序
      const activeLinksSql = `
        SELECT l.*
        FROM links l
        LEFT JOIN tasks t ON l.task_id = t.id
        WHERE l.user_id = ?
        AND l.status = 'active'
        AND l.fail_count < ?
        ORDER BY
          CASE t.priority
            WHEN 'high' THEN 1
            WHEN 'medium' THEN 2
            WHEN 'low' THEN 3
            ELSE 4
          END,
          CASE l.priority
            WHEN 'high' THEN 1
            WHEN 'medium' THEN 2
            WHEN 'low' THEN 3
            ELSE 4
          END,
          l.created_at ASC
      `;

      const [activeLinks] = await db.query(activeLinksSql, [userId, maxRetries]);

      // 遍历所有活跃链接，找到一个符合条件的
      for (const link of activeLinks) {
        // 检查该设备是否已经操作过这个链接
        const hasOperatedBefore = await OperationLog.checkDeviceOperatedLink(deviceId, link.id, 'both');
        if (hasOperatedBefore) {
          console.log(`设备(${deviceId})已经操作过链接(${link.id})，跳过分配`);
          continue;
        }

        // 检查冷却时间
        const lastOpSql = `
          SELECT MAX(created_at) as last_operation
          FROM operation_logs
          WHERE link_id = ? AND user_id = ?
        `;

        const [lastOpResult] = await db.query(lastOpSql, [link.id, userId]);
        const lastOp = lastOpResult[0].last_operation;

        if (lastOp) {
          const lastOpTime = new Date(lastOp);
          const now = new Date();
          const diffSeconds = Math.floor((now - lastOpTime) / 1000);

          if (diffSeconds < cooldownSeconds) {
            // console.log(`链接(${link.id})在冷却中，已过${diffSeconds}秒，需要${cooldownSeconds}秒`);
            continue;
          }
        }

        // 检查并发限制 - 使用新的链接设备分配表
        const activeAssignmentsCount = await LinkDeviceAssignment.countActiveAssignments(link.id);

        if (activeAssignmentsCount >= maxConcurrentOps) {
          console.log(`链接(${link.id})并发操作数已达上限: ${activeAssignmentsCount}/${maxConcurrentOps}`);
          continue;
        }

        // 找到符合条件的链接，创建设备分配记录
        await LinkDeviceAssignment.create(link.id, deviceId);
        console.log(`为设备(${deviceId})分配链接(${link.id})`);

        return link;
      }

      console.log('没有找到符合条件的链接');
      return null;
    } catch (error) {
      console.error('智能分配模式错误:', error);
      throw error;
    }
  }

  // 优先级感知的循环分配模式
  static async getNextRoundRobinLink(userId, deviceId) {
    try {
      const LinkDeviceAssignment = require('./link_device_assignment');
      const OperationLog = require('./operation_log');
      const RoundRobinState = require('./round_robin_state');

      // 获取设置
      const maxRetries = await Setting.getValue(userId, 'link.max_retries', '3');
      const maxConcurrentOps = await Setting.getValue(userId, 'link.max_concurrent_operations', '3');
      const cooldownSeconds = await Setting.getValue(userId, 'link.cooldown_seconds', '60');

      console.log(`🎯 优先级循环分配 - 用户(${userId}), 设备(${deviceId})`);

      // 按优先级顺序检查：高 → 中 → 低
      const priorities = ['high', 'medium', 'low'];

      for (const priority of priorities) {
        console.log(`🔍 检查${priority}优先级链接...`);

        // 获取该优先级的所有活跃链接
        const linksSql = `
          SELECT l.*
          FROM links l
          LEFT JOIN tasks t ON l.task_id = t.id
          WHERE l.user_id = ?
          AND l.status = 'active'
          AND l.fail_count < ?
          AND (
            (t.priority = ? AND t.priority IS NOT NULL) OR
            (l.priority = ? AND t.priority IS NULL)
          )
          ORDER BY l.created_at ASC
        `;

        const [priorityLinks] = await db.query(linksSql, [userId, maxRetries, priority, priority]);

        if (priorityLinks.length === 0) {
          console.log(`📭 ${priority}优先级没有链接，检查下一优先级`);
          continue;
        }

        console.log(`📋 ${priority}优先级有 ${priorityLinks.length} 个链接，开始内部循环`);

        // 在该优先级内部进行完整的循环检查
        let foundAvailableLink = false;

        // 尝试该优先级的所有链接，确保能找到可用的
        for (let attempt = 0; attempt < priorityLinks.length; attempt++) {
          // 获取当前优先级的循环索引
          const roundRobinIndex = await RoundRobinState.getNextRoundRobinIndex(userId, priority, priorityLinks.length);
          const selectedLink = priorityLinks[roundRobinIndex];

          console.log(`🔄 ${priority}优先级循环索引: ${roundRobinIndex}/${priorityLinks.length-1}, 链接ID: ${selectedLink.id}`);

          // 检查选中的链接是否可用
          let isAvailable = true;
          let skipReason = '';

          // 检查并发限制
          const activeAssignmentsCount = await LinkDeviceAssignment.countActiveAssignments(selectedLink.id);
          if (activeAssignmentsCount >= maxConcurrentOps) {
            isAvailable = false;
            skipReason = `并发满(${activeAssignmentsCount}/${maxConcurrentOps})`;
          }

          // 检查冷却时间
          if (isAvailable) {
            const lastOpSql = `
              SELECT MAX(created_at) as last_operation
              FROM operation_logs
              WHERE link_id = ? AND user_id = ?
            `;

            const [lastOpResult] = await db.query(lastOpSql, [selectedLink.id, userId]);
            const lastOp = lastOpResult[0].last_operation;

            if (lastOp) {
              const lastOpTime = new Date(lastOp);
              const now = new Date();
              const diffSeconds = Math.floor((now - lastOpTime) / 1000);

              if (diffSeconds < cooldownSeconds) {
                isAvailable = false;
                skipReason = `冷却中(${diffSeconds}/${cooldownSeconds}s)`;
              }
            }
          }

          // 检查该设备是否已经操作过这个链接
          if (isAvailable) {
            const hasOperatedBefore = await OperationLog.checkDeviceOperatedLink(deviceId, selectedLink.id, 'both');
            if (hasOperatedBefore) {
              isAvailable = false;
              skipReason = '已操作过';
            }
          }

          if (isAvailable) {
            // 找到可用链接，创建分配记录
            await LinkDeviceAssignment.create(selectedLink.id, deviceId);
            console.log(`✅ 设备(${deviceId}) → 链接(${selectedLink.id})[${priority}] 索引:${roundRobinIndex}`);
            return selectedLink;
          } else {
            // 链接不可用，记录原因并继续该优先级的下一个
            console.log(`❌ 链接(${selectedLink.id})[${priority}] 跳过: ${skipReason}`);
          }
        }

        // 该优先级的所有链接都检查完了，都不可用
        console.log(`🚫 ${priority}优先级所有链接都不可用，检查下一优先级`);
      }

      console.log('🔄 所有优先级都检查完毕，无可用链接');
      return null;
    } catch (error) {
      console.error('优先级循环分配模式错误:', error);
      throw error;
    }
  }

  // 更新链接操作状态和计数
  static async updateOperationStatus(linkId, deviceId, status, operationType, beforeLikeCount, beforeCollectCount, errorMessage) {
    try {
      const link = await this.getById(linkId);
      if (!link) {
        throw new Error('链接不存在');
      }
      
      // 无论操作成功失败，都增加操作次数
      let updateOperationsSQL = '';
      if (operationType === 'like' || operationType === 'both') {
        updateOperationsSQL += 'like_operations = like_operations + 1, ';
      }
      if (operationType === 'collect' || operationType === 'both') {
        updateOperationsSQL += 'collect_operations = collect_operations + 1, ';
      }
      
      // 更新操作次数
      if (updateOperationsSQL) {
        await db.query(`UPDATE links SET ${updateOperationsSQL} last_operation_time = NOW() WHERE id = ?`, [linkId]);
      }
      
      // 如果操作成功，处理初始值和当前值更新
      if (status === 'success') {
        // 处理初始点赞数
        if (beforeLikeCount !== undefined) {
          // 检查是否在时间窗口内
          const timeWindowMinutes = 1; // 1分钟时间窗口
          let shouldUpdateOriginalLikes = false;
          
          // 如果初始值未设置或为0，直接更新
          if (link.original_likes === 0 || link.original_likes_set_time === null) {
            shouldUpdateOriginalLikes = true;
          } 
          // 如果初始值已设置，检查是否在时间窗口内且新值更小
          else if (link.original_likes_set_time) {
            const setTime = new Date(link.original_likes_set_time);
            const now = new Date();
            const diffMinutes = (now - setTime) / (1000 * 60);
            
            if (diffMinutes <= timeWindowMinutes && beforeLikeCount < link.original_likes) {
              shouldUpdateOriginalLikes = true;
              console.log(`链接(${linkId})在时间窗口内(${diffMinutes.toFixed(2)}分钟)收到更小的初始点赞数: ${beforeLikeCount} < ${link.original_likes}`);
            }
          }
          
          // 更新初始点赞数
          if (shouldUpdateOriginalLikes) {
            await db.query('UPDATE links SET original_likes = ?, original_likes_set_time = NOW() WHERE id = ?', [
              beforeLikeCount,
              linkId
            ]);
            // console.log(`更新链接(${linkId})初始点赞数为: ${beforeLikeCount}`);
          }
          
          // 更新当前点赞数（只更新更大的值）
          if (beforeLikeCount > link.current_likes) {
            await db.query('UPDATE links SET current_likes = ? WHERE id = ?', [
              beforeLikeCount,
              linkId
            ]);
            // console.log(`更新链接(${linkId})当前点赞数为: ${beforeLikeCount}`);
          }
        }
        
        // 处理初始收藏数
        if (beforeCollectCount !== undefined) {
          // 检查是否在时间窗口内
          const timeWindowMinutes = 1; // 1分钟时间窗口
          let shouldUpdateOriginalCollects = false;
          
          // 如果初始值未设置或为0，直接更新
          if (link.original_collects === 0 || link.original_collects_set_time === null) {
            shouldUpdateOriginalCollects = true;
          } 
          // 如果初始值已设置，检查是否在时间窗口内且新值更小
          else if (link.original_collects_set_time) {
            const setTime = new Date(link.original_collects_set_time);
            const now = new Date();
            const diffMinutes = (now - setTime) / (1000 * 60);
            
            if (diffMinutes <= timeWindowMinutes && beforeCollectCount < link.original_collects) {
              shouldUpdateOriginalCollects = true;
              console.log(`链接(${linkId})在时间窗口内(${diffMinutes.toFixed(2)}分钟)收到更小的初始收藏数: ${beforeCollectCount} < ${link.original_collects}`);
            }
          }
          
          // 更新初始收藏数
          if (shouldUpdateOriginalCollects) {
            await db.query('UPDATE links SET original_collects = ?, original_collects_set_time = NOW() WHERE id = ?', [
              beforeCollectCount,
              linkId
            ]);
            // console.log(`更新链接(${linkId})初始收藏数为: ${beforeCollectCount}`);
          }
          
          // 更新当前收藏数（只更新更大的值）
          if (beforeCollectCount > link.current_collects) {
            await db.query('UPDATE links SET current_collects = ? WHERE id = ?', [
              beforeCollectCount,
              linkId
            ]);
            // console.log(`更新链接(${linkId})当前收藏数为: ${beforeCollectCount}`);
          }
        }
      }
      
      return true;
    } catch (error) {
      console.error('更新链接操作状态错误:', error);
      throw error;
    }
  }

  // 检查链接是否已完成目标
  static async checkLinkCompletion(linkId) {
    try {
      const link = await this.getById(linkId);
      if (!link) {
        throw new Error('链接不存在');
      }
      
      // 计算实际增加的点赞和收藏数
      const actualLikeIncrease = link.current_likes - link.original_likes;
      const actualCollectIncrease = link.current_collects - link.original_collects;
      
      // 使用单独的目标字段，如果有设置的话
      const likeTarget = link.like_target || link.target_likes || 0;
      const collectTarget = link.collect_target || link.target_collects || 0;
      
      // 检查是否所有目标都已达成
      const likesCompleted = likeTarget === 0 || actualLikeIncrease >= likeTarget;
      const collectsCompleted = collectTarget === 0 || actualCollectIncrease >= collectTarget;
      
      // console.log(`链接(${linkId})目标检查 - 点赞增加: ${actualLikeIncrease}/${likeTarget} (${likesCompleted ? '已完成' : '未完成'}), 收藏增加: ${actualCollectIncrease}/${collectTarget} (${collectsCompleted ? '已完成' : '未完成'})`);
      
      // 只有当所有目标都达成时才将状态设置为"completed"
      if (likesCompleted && collectsCompleted) {
        console.log(`链接(${linkId})所有目标已达成，设置状态为"completed"`);
        await this.updateStatus(linkId, 'completed');
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('检查链接完成状态错误:', error);
      throw error;
    }
  }

  // 获取操作次数最多的热门链接
  static async getTopLinksByOperations(userId, limit = 5) {
    try {
      const sql = `
        SELECT l.*,
          COUNT(ol.id) as operation_count,
          SUM(CASE WHEN ol.operation_type = 'like' THEN 1 ELSE 0 END) as like_operations,
          SUM(CASE WHEN ol.operation_type = 'collect' THEN 1 ELSE 0 END) as collect_operations,
          MAX(ol.created_at) as last_operation_time
        FROM links l
        LEFT JOIN operation_logs ol ON l.id = ol.link_id
        WHERE l.user_id = ?
        GROUP BY l.id
        ORDER BY operation_count DESC, last_operation_time DESC
        LIMIT ?
      `;

      const [results] = await db.query(sql, [userId, limit]);
      return results || [];
    } catch (error) {
      console.error('获取热门链接错误:', error);
      return [];
    }
  }
}

module.exports = Link;